"""
Options Data Fetching and Processing

Migrated from the standalone options analysis system.
Handles real-time options data acquisition, caching, and market analysis.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import yfinance as yf
from yahooquery import Ticker
import time
import json

from src.utils.logger import get_logger
from src.utils.stock_cache import StockCache


class OptionsDataManager:
    """Manages options data fetching, caching, and market analysis."""

    def __init__(self):
        self.logger = get_logger()
        self.stock_cache = StockCache()

    def validate_symbol(self, symbol: str) -> Dict[str, Any]:
        """
        Validate a symbol and check if it likely supports options trading.
        Returns validation info including whether options might be available.
        """
        validation_result = {
            'symbol': symbol,
            'is_valid': False,
            'has_market_data': False,
            'likely_has_options': False,
            'symbol_type': 'unknown',
            'exchange': None,
            'market_cap': None,
            'error': None
        }

        try:
            # Use yahooquery for quick validation
            ticker = Ticker(symbol)
            summary = ticker.summary_detail

            if isinstance(summary, dict) and symbol in summary:
                symbol_data = summary[symbol]
                validation_result['is_valid'] = True
                validation_result['has_market_data'] = True

                # Extract market data
                market_cap = symbol_data.get('marketCap')
                if market_cap:
                    validation_result['market_cap'] = market_cap
                    # Large cap stocks (>$1B) are more likely to have options
                    validation_result['likely_has_options'] = market_cap > 1_000_000_000

                # Check if it's an ETF or stock
                if symbol.endswith('LL') or 'leveraged' in str(symbol_data).lower():
                    validation_result['symbol_type'] = 'leveraged_etf'
                    # Leveraged ETFs may or may not have options
                    validation_result['likely_has_options'] = market_cap and market_cap > 100_000_000
                elif market_cap and market_cap > 10_000_000_000:
                    validation_result['symbol_type'] = 'large_cap_stock'
                    validation_result['likely_has_options'] = True
                elif market_cap and market_cap > 1_000_000_000:
                    validation_result['symbol_type'] = 'mid_cap_stock'
                    validation_result['likely_has_options'] = True
                else:
                    validation_result['symbol_type'] = 'small_cap_or_etf'
                    validation_result['likely_has_options'] = False

        except Exception as e:
            validation_result['error'] = str(e)
            self.logger.warning(f"Symbol validation failed for {symbol}: {str(e)}")

        return validation_result
        
    def fetch_data_for_batch(self, symbols: List[str], config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Fetch options and price data for a batch of symbols.
        Migrated from utils.py with enhanced error handling and caching.
        """
        batch_size = config.get('data_acquisition', {}).get('batch_size', 20)
        use_cache = config.get('data_acquisition', {}).get('use_cache', True)
        
        all_options = []
        all_prices = {}
        
        self.logger.info(f"Fetching data for {len(symbols)} symbols in batch")

        # Validate symbols first and provide early feedback
        validation_results = {}
        for symbol in symbols:
            validation_results[symbol] = self.validate_symbol(symbol)

        # Log validation results
        valid_symbols = [s for s, v in validation_results.items() if v['is_valid']]
        invalid_symbols = [s for s, v in validation_results.items() if not v['is_valid']]
        unlikely_options = [s for s, v in validation_results.items() if v['is_valid'] and not v['likely_has_options']]

        if invalid_symbols:
            self.logger.warning(f"Invalid symbols detected: {invalid_symbols}")
        if unlikely_options:
            self.logger.info(f"Symbols unlikely to have options: {unlikely_options}")
        if valid_symbols:
            self.logger.info(f"Valid symbols to process: {valid_symbols}")
        
        # Initialize cached_options as empty DataFrame
        cached_options = pd.DataFrame()

        # Check cache first if enabled
        if use_cache:
            # Get list of symbols that have cached options data
            cached_symbols_list = self.stock_cache.get_cached_options_symbols()
            if cached_symbols_list:
                self.logger.info(f"Found cached options data for {len(cached_symbols_list)} symbols")
                # For now, we'll still fetch fresh data but this is where cached data would be loaded
                # TODO: Implement actual cache retrieval when cache storage is implemented

        # Fetch fresh data for symbols not in cache or if cache is disabled
        symbols_to_fetch = symbols
        if use_cache and not cached_options.empty:
            cached_symbols = cached_options['symbol'].unique()
            symbols_to_fetch = [s for s in symbols if s not in cached_symbols]
        
        if symbols_to_fetch:
            self.logger.info(f"Fetching fresh data for {len(symbols_to_fetch)} symbols")
            
            # Process in smaller batches to avoid rate limits
            for i in range(0, len(symbols_to_fetch), batch_size):
                batch = symbols_to_fetch[i:i + batch_size]
                self.logger.info(f"Processing batch {i//batch_size + 1}: {batch}")
                
                try:
                    # Fetch options data
                    options_df = self._fetch_options_for_symbols(batch)
                    if not options_df.empty:
                        all_options.append(options_df)
                        
                        # Cache the fresh data
                        if use_cache:
                            # Note: Cache functionality would be implemented here
                            pass
                    
                    # Fetch current prices
                    prices = self._fetch_current_prices(batch)
                    all_prices.update(prices)
                    
                    # Rate limiting
                    if i + batch_size < len(symbols_to_fetch):
                        time.sleep(1)  # 1 second delay between batches
                        
                except Exception as e:
                    self.logger.error(f"Error fetching data for batch {batch}: {str(e)}")
                    continue
        
        # Combine all options data
        combined_options = pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()

        # Add current prices to options data
        if not combined_options.empty and all_prices:
            combined_options['currentPrice'] = combined_options['symbol'].map(all_prices)
            combined_options = combined_options.dropna(subset=['currentPrice'])

        # Provide detailed feedback about results
        if combined_options.empty:
            successful_symbols = []
            failed_symbols = symbols
            error_msg = f"No options data retrieved for symbols: {failed_symbols}. "
            error_msg += "Possible reasons: (1) Symbols don't support options trading, "
            error_msg += "(2) Network connectivity issues, (3) Invalid symbols, or (4) API rate limiting."
            self.logger.error(error_msg)
        else:
            successful_symbols = combined_options['symbol'].unique().tolist() if 'symbol' in combined_options.columns else []
            failed_symbols = [s for s in symbols if s not in successful_symbols]

            self.logger.info(f"Successfully fetched {len(combined_options)} options records for {len(successful_symbols)} symbols: {successful_symbols}")
            if failed_symbols:
                self.logger.warning(f"Failed to fetch options data for symbols: {failed_symbols}")

        return combined_options, all_prices
    
    def _fetch_options_for_symbols(self, symbols: List[str]) -> pd.DataFrame:
        """Fetch options data for a list of symbols."""
        all_options = []
        
        for symbol in symbols:
            try:
                self.logger.debug(f"Fetching options for {symbol}")
                options_df = self._fetch_single_symbol_options(symbol)
                if not options_df.empty:
                    all_options.append(options_df)
                    
            except Exception as e:
                self.logger.warning(f"Failed to fetch options for {symbol}: {str(e)}")
                continue
        
        return pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()
    
    def _fetch_single_symbol_options(self, symbol: str) -> pd.DataFrame:
        """Fetch options data for a single symbol using yahooquery with enhanced error handling."""
        try:
            ticker = Ticker(symbol)

            # First, verify the symbol exists and get basic info
            try:
                summary = ticker.summary_detail
                if isinstance(summary, dict) and symbol in summary:
                    self.logger.info(f"Symbol {symbol} found with market data")
                else:
                    self.logger.warning(f"Symbol {symbol} not found or has no market data")
                    return pd.DataFrame()
            except Exception as e:
                self.logger.warning(f"Could not verify symbol {symbol}: {str(e)}")

            # Get option expiration dates
            option_chain = ticker.option_chain

            # Handle different response types from yahooquery
            if option_chain is None:
                self.logger.warning(f"No options chain response for {symbol}")
                return pd.DataFrame()

            # Check if it's an error response
            if isinstance(option_chain, dict) and 'error' in str(option_chain).lower():
                self.logger.warning(f"Options chain error for {symbol}: {option_chain}")
                return pd.DataFrame()

            # Check if symbol exists in the response
            if not isinstance(option_chain, dict) or symbol not in option_chain:
                self.logger.warning(f"No options data available for {symbol} - symbol may not have options trading")
                return pd.DataFrame()

            symbol_data = option_chain[symbol]

            # Check if the response contains error information
            if isinstance(symbol_data, dict) and 'error' in symbol_data:
                self.logger.warning(f"Options error for {symbol}: {symbol_data['error']}")
                return pd.DataFrame()

            if not isinstance(symbol_data, dict) or ('calls' not in symbol_data and 'puts' not in symbol_data):
                self.logger.warning(f"No calls or puts data for {symbol} - options may not be available for this symbol")
                return pd.DataFrame()

            options_list = []

            # Process calls
            if 'calls' in symbol_data and symbol_data['calls'] is not None:
                try:
                    calls_df = pd.DataFrame(symbol_data['calls'])
                    if not calls_df.empty:
                        calls_df['optionType'] = 'calls'
                        calls_df['symbol'] = symbol
                        options_list.append(calls_df)
                        self.logger.info(f"Found {len(calls_df)} call options for {symbol}")
                except Exception as e:
                    self.logger.warning(f"Error processing calls for {symbol}: {str(e)}")

            # Process puts
            if 'puts' in symbol_data and symbol_data['puts'] is not None:
                try:
                    puts_df = pd.DataFrame(symbol_data['puts'])
                    if not puts_df.empty:
                        puts_df['optionType'] = 'puts'
                        puts_df['symbol'] = symbol
                        options_list.append(puts_df)
                        self.logger.info(f"Found {len(puts_df)} put options for {symbol}")
                except Exception as e:
                    self.logger.warning(f"Error processing puts for {symbol}: {str(e)}")

            if not options_list:
                self.logger.warning(f"No valid options data found for {symbol} after processing")
                return pd.DataFrame()

            # Combine calls and puts
            combined_df = pd.concat(options_list, ignore_index=True)

            # Standardize column names and add calculated fields
            combined_df = self._standardize_options_data(combined_df)

            self.logger.info(f"Successfully processed {len(combined_df)} total options for {symbol}")
            return combined_df

        except Exception as e:
            self.logger.error(f"Error fetching options for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def _standardize_options_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize options data format and add calculated fields."""
        if df.empty:
            return df
        
        # Ensure required columns exist
        required_columns = ['strike', 'bid', 'ask', 'expiration', 'impliedVolatility']
        for col in required_columns:
            if col not in df.columns:
                df[col] = np.nan
        
        # Calculate DTE (Days to Expiration)
        if 'expiration' in df.columns:
            df['expiration'] = pd.to_datetime(df['expiration'])
            df['dte'] = (df['expiration'] - datetime.now()).dt.days
            
            # Filter out expired options and very short-term options
            df = df[df['dte'] > 0]
        
        # Clean numeric columns
        numeric_columns = ['strike', 'bid', 'ask', 'impliedVolatility', 'volume', 'openInterest']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Filter out options with no bid/ask data
        df = df.dropna(subset=['bid', 'ask'])
        df = df[(df['bid'] > 0) & (df['ask'] > 0)]
        
        # Calculate mid price
        df['mid'] = (df['bid'] + df['ask']) / 2
        
        # Filter by liquidity (basic filter)
        df = df[df['bid'] > 0.05]  # Minimum bid of $0.05
        
        return df
    
    def _fetch_current_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Fetch current stock prices for symbols using the enhanced stock cache."""
        # Use the stock cache for better performance and caching
        return self.stock_cache.get_current_prices(symbols)
    
    def detect_volatility_regime(self, symbols: List[str]) -> str:
        """
        Detect current market volatility regime.
        Migrated from utils.py with simplified implementation.
        """
        try:
            # Use VIX as a proxy for market volatility
            vix = yf.Ticker("^VIX")
            vix_data = vix.history(period="5d")
            
            if vix_data.empty:
                return "Normal"
            
            current_vix = vix_data['Close'].iloc[-1]
            
            if current_vix > 30:
                return "High Volatility"
            elif current_vix < 15:
                return "Low Volatility"
            else:
                return "Normal"
                
        except Exception as e:
            self.logger.warning(f"Error detecting volatility regime: {str(e)}")
            return "Normal"
    
    def calculate_market_stress_indicator(self, symbols: List[str]) -> float:
        """
        Calculate market stress indicator (0-100 scale).
        Migrated from utils.py with simplified implementation.
        """
        try:
            # Simple implementation using VIX
            vix = yf.Ticker("^VIX")
            vix_data = vix.history(period="5d")
            
            if vix_data.empty:
                return 50.0  # Neutral
            
            current_vix = vix_data['Close'].iloc[-1]
            
            # Convert VIX to 0-100 stress scale
            # VIX of 10 = 0 stress, VIX of 50+ = 100 stress
            stress_level = min(100, max(0, (current_vix - 10) * 2.5))
            
            return float(stress_level)
            
        except Exception as e:
            self.logger.warning(f"Error calculating market stress: {str(e)}")
            return 50.0  # Neutral fallback
    
    def get_market_conditions(self, symbols: List[str]) -> Dict[str, Any]:
        """Get comprehensive market conditions analysis."""
        return {
            'volatility_regime': self.detect_volatility_regime(symbols),
            'stress_indicator': self.calculate_market_stress_indicator(symbols),
            'timestamp': datetime.now().isoformat()
        }

    def refresh_options_data(self, symbols: List[str], force_refresh: bool = False) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Refresh options data for specified symbols.

        Args:
            symbols: List of stock symbols to refresh
            force_refresh: If True, bypass cache and fetch fresh data

        Returns:
            Tuple of (options_dataframe, current_prices)
        """
        if force_refresh:
            # Clear cache for these symbols
            # Note: Cache clearing functionality would be implemented here
            pass

        # Fetch fresh data
        config = {
            'data_acquisition': {
                'batch_size': min(10, len(symbols)),
                'use_cache': not force_refresh
            }
        }

        return self.fetch_data_for_batch(symbols, config)

    def get_cached_symbols_status(self) -> Dict[str, Any]:
        """Get status of cached symbols and their freshness."""
        cached_options_symbols = self.stock_cache.get_cached_options_symbols()

        status = {
            'cached_options_symbols': cached_options_symbols,
            'total_cached': len(cached_options_symbols),
            'last_updated': datetime.now().isoformat()
        }

        return status

    def schedule_data_refresh(self, symbols: List[str], interval_minutes: int = 15):
        """
        Schedule periodic refresh of options data.
        Note: This would typically be implemented with a task scheduler like Celery
        For now, this is a placeholder for future implementation.
        """
        self.logger.info(f"Scheduling data refresh for {len(symbols)} symbols every {interval_minutes} minutes")
        # TODO: Implement with background task scheduler
        pass
