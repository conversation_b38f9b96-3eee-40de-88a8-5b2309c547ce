"""
Options Data Fetching and Processing

Migrated from the standalone options analysis system.
Handles real-time options data acquisition, caching, and market analysis.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import yfinance as yf
from yahooquery import Ticker
import time
import json

from src.utils.logger import get_logger
from src.utils.stock_cache import StockCache


class OptionsDataManager:
    """Manages options data fetching, caching, and market analysis."""

    def __init__(self):
        self.logger = get_logger()
        self.stock_cache = StockCache()
        
    def fetch_data_for_batch(self, symbols: List[str], config: Dict[str, Any]) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Fetch options and price data for a batch of symbols.
        Migrated from utils.py with enhanced error handling and caching.
        """
        batch_size = config.get('data_acquisition', {}).get('batch_size', 20)
        use_cache = config.get('data_acquisition', {}).get('use_cache', True)
        
        all_options = []
        all_prices = {}
        
        self.logger.info(f"Fetching data for {len(symbols)} symbols in batch")
        
        # Initialize cached_options as empty DataFrame
        cached_options = pd.DataFrame()

        # Check cache first if enabled
        if use_cache:
            # Get list of symbols that have cached options data
            cached_symbols_list = self.stock_cache.get_cached_options_symbols()
            if cached_symbols_list:
                self.logger.info(f"Found cached options data for {len(cached_symbols_list)} symbols")
                # For now, we'll still fetch fresh data but this is where cached data would be loaded
                # TODO: Implement actual cache retrieval when cache storage is implemented

        # Fetch fresh data for symbols not in cache or if cache is disabled
        symbols_to_fetch = symbols
        if use_cache and not cached_options.empty:
            cached_symbols = cached_options['symbol'].unique()
            symbols_to_fetch = [s for s in symbols if s not in cached_symbols]
        
        if symbols_to_fetch:
            self.logger.info(f"Fetching fresh data for {len(symbols_to_fetch)} symbols")
            
            # Process in smaller batches to avoid rate limits
            for i in range(0, len(symbols_to_fetch), batch_size):
                batch = symbols_to_fetch[i:i + batch_size]
                self.logger.info(f"Processing batch {i//batch_size + 1}: {batch}")
                
                try:
                    # Fetch options data
                    options_df = self._fetch_options_for_symbols(batch)
                    if not options_df.empty:
                        all_options.append(options_df)
                        
                        # Cache the fresh data
                        if use_cache:
                            # Note: Cache functionality would be implemented here
                            pass
                    
                    # Fetch current prices
                    prices = self._fetch_current_prices(batch)
                    all_prices.update(prices)
                    
                    # Rate limiting
                    if i + batch_size < len(symbols_to_fetch):
                        time.sleep(1)  # 1 second delay between batches
                        
                except Exception as e:
                    self.logger.error(f"Error fetching data for batch {batch}: {str(e)}")
                    continue
        
        # Combine all options data
        combined_options = pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()
        
        # Add current prices to options data
        if not combined_options.empty and all_prices:
            combined_options['currentPrice'] = combined_options['symbol'].map(all_prices)
            combined_options = combined_options.dropna(subset=['currentPrice'])
        
        self.logger.info(f"Total options records: {len(combined_options)}, Price data for {len(all_prices)} symbols")
        return combined_options, all_prices
    
    def _fetch_options_for_symbols(self, symbols: List[str]) -> pd.DataFrame:
        """Fetch options data for a list of symbols."""
        all_options = []
        
        for symbol in symbols:
            try:
                self.logger.debug(f"Fetching options for {symbol}")
                options_df = self._fetch_single_symbol_options(symbol)
                if not options_df.empty:
                    all_options.append(options_df)
                    
            except Exception as e:
                self.logger.warning(f"Failed to fetch options for {symbol}: {str(e)}")
                continue
        
        return pd.concat(all_options, ignore_index=True) if all_options else pd.DataFrame()
    
    def _fetch_single_symbol_options(self, symbol: str) -> pd.DataFrame:
        """Fetch options data for a single symbol using yahooquery."""
        try:
            ticker = Ticker(symbol)
            
            # Get option expiration dates
            option_chain = ticker.option_chain
            
            if not option_chain or symbol not in option_chain:
                self.logger.warning(f"No options data available for {symbol}")
                return pd.DataFrame()
            
            symbol_data = option_chain[symbol]
            
            if 'calls' not in symbol_data and 'puts' not in symbol_data:
                self.logger.warning(f"No calls or puts data for {symbol}")
                return pd.DataFrame()
            
            options_list = []
            
            # Process calls
            if 'calls' in symbol_data:
                calls_df = pd.DataFrame(symbol_data['calls'])
                if not calls_df.empty:
                    calls_df['optionType'] = 'calls'
                    calls_df['symbol'] = symbol
                    options_list.append(calls_df)
            
            # Process puts
            if 'puts' in symbol_data:
                puts_df = pd.DataFrame(symbol_data['puts'])
                if not puts_df.empty:
                    puts_df['optionType'] = 'puts'
                    puts_df['symbol'] = symbol
                    options_list.append(puts_df)
            
            if not options_list:
                return pd.DataFrame()
            
            # Combine calls and puts
            combined_df = pd.concat(options_list, ignore_index=True)
            
            # Standardize column names and add calculated fields
            combined_df = self._standardize_options_data(combined_df)
            
            return combined_df
            
        except Exception as e:
            self.logger.error(f"Error fetching options for {symbol}: {str(e)}")
            return pd.DataFrame()
    
    def _standardize_options_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize options data format and add calculated fields."""
        if df.empty:
            return df
        
        # Ensure required columns exist
        required_columns = ['strike', 'bid', 'ask', 'expiration', 'impliedVolatility']
        for col in required_columns:
            if col not in df.columns:
                df[col] = np.nan
        
        # Calculate DTE (Days to Expiration)
        if 'expiration' in df.columns:
            df['expiration'] = pd.to_datetime(df['expiration'])
            df['dte'] = (df['expiration'] - datetime.now()).dt.days
            
            # Filter out expired options and very short-term options
            df = df[df['dte'] > 0]
        
        # Clean numeric columns
        numeric_columns = ['strike', 'bid', 'ask', 'impliedVolatility', 'volume', 'openInterest']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Filter out options with no bid/ask data
        df = df.dropna(subset=['bid', 'ask'])
        df = df[(df['bid'] > 0) & (df['ask'] > 0)]
        
        # Calculate mid price
        df['mid'] = (df['bid'] + df['ask']) / 2
        
        # Filter by liquidity (basic filter)
        df = df[df['bid'] > 0.05]  # Minimum bid of $0.05
        
        return df
    
    def _fetch_current_prices(self, symbols: List[str]) -> Dict[str, float]:
        """Fetch current stock prices for symbols using the enhanced stock cache."""
        # Use the stock cache for better performance and caching
        return self.stock_cache.get_current_prices(symbols)
    
    def detect_volatility_regime(self, symbols: List[str]) -> str:
        """
        Detect current market volatility regime.
        Migrated from utils.py with simplified implementation.
        """
        try:
            # Use VIX as a proxy for market volatility
            vix = yf.Ticker("^VIX")
            vix_data = vix.history(period="5d")
            
            if vix_data.empty:
                return "Normal"
            
            current_vix = vix_data['Close'].iloc[-1]
            
            if current_vix > 30:
                return "High Volatility"
            elif current_vix < 15:
                return "Low Volatility"
            else:
                return "Normal"
                
        except Exception as e:
            self.logger.warning(f"Error detecting volatility regime: {str(e)}")
            return "Normal"
    
    def calculate_market_stress_indicator(self, symbols: List[str]) -> float:
        """
        Calculate market stress indicator (0-100 scale).
        Migrated from utils.py with simplified implementation.
        """
        try:
            # Simple implementation using VIX
            vix = yf.Ticker("^VIX")
            vix_data = vix.history(period="5d")
            
            if vix_data.empty:
                return 50.0  # Neutral
            
            current_vix = vix_data['Close'].iloc[-1]
            
            # Convert VIX to 0-100 stress scale
            # VIX of 10 = 0 stress, VIX of 50+ = 100 stress
            stress_level = min(100, max(0, (current_vix - 10) * 2.5))
            
            return float(stress_level)
            
        except Exception as e:
            self.logger.warning(f"Error calculating market stress: {str(e)}")
            return 50.0  # Neutral fallback
    
    def get_market_conditions(self, symbols: List[str]) -> Dict[str, Any]:
        """Get comprehensive market conditions analysis."""
        return {
            'volatility_regime': self.detect_volatility_regime(symbols),
            'stress_indicator': self.calculate_market_stress_indicator(symbols),
            'timestamp': datetime.now().isoformat()
        }

    def refresh_options_data(self, symbols: List[str], force_refresh: bool = False) -> Tuple[pd.DataFrame, Dict[str, float]]:
        """
        Refresh options data for specified symbols.

        Args:
            symbols: List of stock symbols to refresh
            force_refresh: If True, bypass cache and fetch fresh data

        Returns:
            Tuple of (options_dataframe, current_prices)
        """
        if force_refresh:
            # Clear cache for these symbols
            # Note: Cache clearing functionality would be implemented here
            pass

        # Fetch fresh data
        config = {
            'data_acquisition': {
                'batch_size': min(10, len(symbols)),
                'use_cache': not force_refresh
            }
        }

        return self.fetch_data_for_batch(symbols, config)

    def get_cached_symbols_status(self) -> Dict[str, Any]:
        """Get status of cached symbols and their freshness."""
        cached_options_symbols = self.stock_cache.get_cached_options_symbols()

        status = {
            'cached_options_symbols': cached_options_symbols,
            'total_cached': len(cached_options_symbols),
            'last_updated': datetime.now().isoformat()
        }

        return status

    def schedule_data_refresh(self, symbols: List[str], interval_minutes: int = 15):
        """
        Schedule periodic refresh of options data.
        Note: This would typically be implemented with a task scheduler like Celery
        For now, this is a placeholder for future implementation.
        """
        self.logger.info(f"Scheduling data refresh for {len(symbols)} symbols every {interval_minutes} minutes")
        # TODO: Implement with background task scheduler
        pass
