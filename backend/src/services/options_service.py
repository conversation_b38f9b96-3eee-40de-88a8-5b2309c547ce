"""
Options Analysis Service Layer

This service integrates the complete options analysis functionality into the main system,
providing a clean interface for all options-related operations including:
- Cash-Secured Puts analysis
- Covered Calls analysis
- Iron Condors analysis
- Watchlist management
- Configuration management
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import json
import sqlite3
from loguru import logger

import sqlite3
import os
from src.utils.stock_cache import StockCache
from src.utils.logger import get_logger


def get_db_connection():
    """Get database connection for options service."""
    db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'stock_trading.db')
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn


class OptionsAnalysisService:
    """Main service class for options analysis functionality."""
    
    def __init__(self):
        self.stock_cache = StockCache()
        self.logger = get_logger()
        
    def get_user_watchlist(self, account_id: int, watchlist_id: Optional[int] = None) -> Dict[str, Any]:
        """Get user's watchlist(s) for options analysis."""
        conn = get_db_connection()
        try:
            if watchlist_id:
                query = """
                    SELECT watchlist_id, name, symbols, created_at, updated_at
                    FROM user_watchlists 
                    WHERE account_id = ? AND watchlist_id = ?
                """
                result = conn.execute(query, (account_id, watchlist_id)).fetchone()
                if result:
                    return {
                        'watchlist_id': result[0],
                        'name': result[1],
                        'symbols': json.loads(result[2]),
                        'created_at': result[3],
                        'updated_at': result[4]
                    }
                return None
            else:
                query = """
                    SELECT watchlist_id, name, symbols, created_at, updated_at
                    FROM user_watchlists 
                    WHERE account_id = ?
                    ORDER BY created_at DESC
                """
                results = conn.execute(query, (account_id,)).fetchall()
                return [
                    {
                        'watchlist_id': row[0],
                        'name': row[1],
                        'symbols': json.loads(row[2]),
                        'created_at': row[3],
                        'updated_at': row[4]
                    }
                    for row in results
                ]
        finally:
            conn.close()
    
    def create_or_update_watchlist(self, account_id: int, symbols: List[str], 
                                 name: str = "Default Watchlist", 
                                 watchlist_id: Optional[int] = None) -> int:
        """Create a new watchlist or update an existing one."""
        conn = get_db_connection()
        try:
            symbols_json = json.dumps(symbols)
            current_time = datetime.now().isoformat()
            
            if watchlist_id:
                # Try to update existing watchlist
                cursor = conn.execute("""
                    UPDATE user_watchlists
                    SET symbols = ?, name = ?, updated_at = ?
                    WHERE account_id = ? AND watchlist_id = ?
                """, (symbols_json, name, current_time, account_id, watchlist_id))

                # Check if the update affected any rows
                if cursor.rowcount > 0:
                    conn.commit()
                    return watchlist_id
                else:
                    # Watchlist doesn't exist, create a new one
                    cursor = conn.execute("""
                        INSERT INTO user_watchlists (account_id, name, symbols, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (account_id, name, symbols_json, current_time, current_time))
                    conn.commit()
                    return cursor.lastrowid
            else:
                # Create new watchlist
                cursor = conn.execute("""
                    INSERT INTO user_watchlists (account_id, name, symbols, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (account_id, name, symbols_json, current_time, current_time))
                conn.commit()
                return cursor.lastrowid
        finally:
            conn.close()
    
    def get_strategy_config(self, account_id: int, strategy_type: str) -> Dict[str, Any]:
        """Get strategy configuration for a user and strategy type."""
        conn = get_db_connection()
        try:
            query = """
                SELECT config_data FROM strategy_configs 
                WHERE account_id = ? AND strategy_type = ?
                ORDER BY is_default DESC, created_at DESC
                LIMIT 1
            """
            result = conn.execute(query, (account_id, strategy_type)).fetchone()
            if result:
                return json.loads(result[0])
            else:
                # Return default configuration
                return self._get_default_strategy_config(strategy_type)
        finally:
            conn.close()
    
    def save_strategy_config(self, account_id: int, strategy_type: str, 
                           config_data: Dict[str, Any], is_default: bool = False) -> int:
        """Save strategy configuration for a user."""
        conn = get_db_connection()
        try:
            config_json = json.dumps(config_data)
            current_time = datetime.now().isoformat()
            
            # Always create new config for versioning
            cursor = conn.execute("""
                INSERT INTO strategy_configs
                (account_id, strategy_type, config_data, is_default, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (account_id, strategy_type, config_json, int(is_default), current_time, current_time))
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()
    
    def _get_default_strategy_config(self, strategy_type: str) -> Dict[str, Any]:
        """Get default configuration for a strategy type."""
        default_configs = {
            'cash_secured_puts': {
                'min_dte': 20,
                'max_dte': 60,
                'min_annual_roi': 0.15,
                'max_delta': 0.30,
                'min_buffer_percent': 0.05,
                'sort_by': 'winRateScore',
                'max_candidates': 10
            },
            'covered_calls': {
                'min_dte': 20,
                'max_dte': 60,
                'min_annual_roi': 0.10,
                'max_delta': 0.30,
                'min_upside_buffer': 0.02,
                'sort_by': 'annualizedRoi',
                'max_candidates': 10
            },
            'iron_condors': {
                'min_dte': 30,
                'max_dte': 60,
                'min_annual_roi': 0.20,
                'target_delta_short_put': 0.20,
                'target_delta_short_call': 0.20,
                'min_wing_width': 5,
                'max_wing_width': 20,
                'sort_by': 'winRateScore',
                'max_candidates': 5
            },

        }
        return default_configs.get(strategy_type, {})
    
    def cache_options_data(self, options_data: pd.DataFrame) -> None:
        """Cache options data to database."""
        if options_data.empty:
            return
            
        conn = get_db_connection()
        try:
            # Clear old data (older than 1 day)
            cutoff_time = (datetime.now() - timedelta(days=1)).isoformat()
            conn.execute("DELETE FROM cached_options WHERE last_updated < ?", (cutoff_time,))
            
            # Insert new data
            for _, row in options_data.iterrows():
                conn.execute("""
                    INSERT OR REPLACE INTO cached_options 
                    (symbol, expiration_date, strike, option_type, bid, ask, 
                     implied_volatility, volume, open_interest, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row['symbol'], row['expiration'], row['strike'], row['optionType'],
                    row.get('bid'), row.get('ask'), row.get('impliedVolatility'),
                    row.get('volume'), row.get('openInterest'), datetime.now().isoformat()
                ))
            conn.commit()
            self.logger.info(f"Cached {len(options_data)} options records")
        finally:
            conn.close()
    
    def get_cached_options_data(self, symbols: List[str], 
                              max_age_hours: int = 4) -> pd.DataFrame:
        """Retrieve cached options data from database."""
        conn = get_db_connection()
        try:
            cutoff_time = (datetime.now() - timedelta(hours=max_age_hours)).isoformat()
            placeholders = ','.join(['?' for _ in symbols])
            query = f"""
                SELECT symbol, expiration_date, strike, option_type, bid, ask,
                       implied_volatility, volume, open_interest, last_updated
                FROM cached_options 
                WHERE symbol IN ({placeholders}) AND last_updated > ?
                ORDER BY symbol, expiration_date, strike
            """
            
            results = conn.execute(query, symbols + [cutoff_time]).fetchall()
            
            if not results:
                return pd.DataFrame()
                
            df = pd.DataFrame(results, columns=[
                'symbol', 'expiration', 'strike', 'optionType', 'bid', 'ask',
                'impliedVolatility', 'volume', 'openInterest', 'last_updated'
            ])
            
            return df
        finally:
            conn.close()
    
    def save_analysis_results(self, account_id: int, strategy_type: str, 
                            symbols: List[str], results_data: Dict[str, Any],
                            market_conditions: Optional[Dict[str, Any]] = None) -> int:
        """Save analysis results to database."""
        conn = get_db_connection()
        try:
            symbols_json = json.dumps(symbols)
            results_json = json.dumps(results_data)
            market_json = json.dumps(market_conditions) if market_conditions else None
            
            cursor = conn.execute("""
                INSERT INTO options_analysis_results 
                (account_id, strategy_type, symbols, results_data, market_conditions, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (account_id, strategy_type, symbols_json, results_json, 
                  market_json, datetime.now().isoformat()))
            conn.commit()
            return cursor.lastrowid
        finally:
            conn.close()
