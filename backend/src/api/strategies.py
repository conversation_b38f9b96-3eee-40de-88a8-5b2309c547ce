from flask import jsonify, request
from typing import Dict, List, Optional
from datetime import datetime
from ..models.strategy import Stock
from ..models.risk_manager import RiskManager
from ..models import (
    PerformanceMonitor,
)
from ..models.prediction_model.prediction_pipeline import get_future_predictions_for_ticker
import yfinance as yf
import pandas as pd
import numpy as np
from ..utils.stock_cache import StockCache
from ..utils.logger import get_logger
from . import strategies_bp, get_db_connection
import traceback
from ..utils.llm import LLMService
from ..utils.calculations import calculate_twr, calculate_current_market_value
from ..utils.technical_indicators import calculate_wilder_rsi, calculate_macd, calculate_supersmoother
from ..services.options_service import OptionsAnalysisService
from ..services.options_strategies import OptionsStrategiesAnalyzer
from ..services.options_data import OptionsDataManager
# 获取logger实例
logger = get_logger()
llm_service = LLMService()

# Initialize options analysis services
options_service = OptionsAnalysisService()
options_analyzer = OptionsStrategiesAnalyzer()
options_data_manager = OptionsDataManager()

# Initialize components
config = {
    'max_position_size': 0.05,
    'max_portfolio_vol': 0.15,
    'stop_loss_pct': 0.15,
    'sector_exposure_limit': 0.25,
    'max_turnover': 3.0,
    'min_trade_size': 100
}

performance_monitor = PerformanceMonitor()
stock_cache = StockCache()

@strategies_bp.route("/stocks", methods=['GET'], strict_slashes=False)
def get_available_stocks():
    """获取可用的股票列表，包括已持仓和缓存的股票"""
    conn = None
    try:
        logger.info("开始处理 /stocks 请求")
        conn = get_db_connection()
        account_id = request.args.get('account_id', None)
        logger.info(f"请求参数 account_id: {account_id}")
        
        # 获取已持仓的股票
        logger.info("执行持仓股票查询")
        positions = conn.execute('''
            SELECT DISTINCT symbol 
            FROM transactions 
            WHERE account_id = ?
            GROUP BY symbol 
            HAVING SUM(quantity) > 0 AND symbol != 'CASH'
        ''', [account_id]).fetchall()
        position_symbols = [row[0] for row in positions]
        logger.info(f"查询到的持仓股票: {position_symbols}")
        
        # 获取所有缓存的股票数据
        logger.info("执行缓存股票查询")
        cached_symbols = conn.execute('''
            SELECT DISTINCT symbol 
            FROM cached_prices
        ''').fetchall()
        cached_symbols = [row[0] for row in cached_symbols]
        logger.info(f"查询到的缓存股票: {cached_symbols}")
        
        response_data = {
            "position_symbols": position_symbols,
            "cached_symbols": list(set(cached_symbols))  # 去重
        }
        logger.info(f"返回数据: {response_data}")
        return jsonify(response_data)
    except Exception as e:
        logger.opt(exception=True).error(f"处理 /stocks 请求时发生错误: {str(e)}")
        return jsonify({'error': str(e)}), 500
    finally:
        if conn:
            conn.close()
            logger.info("数据库连接已关闭")

@strategies_bp.route("/analysis", methods=['GET'], strict_slashes=False)
def get_strategy_analysis():
    """分析指定股票的策略建议"""
    conn = None
    try:
        logger.info("开始处理策略分析请求")
        symbols = request.args.getlist('symbols')
        account_id_str = request.args.get('account_id', None) # Get as string first
        logger.info(f"请求参数 - symbols: {symbols}, account_id: {account_id_str}")
        
        # Convert account_id to integer if provided
        account_id = None
        if account_id_str:
            try:
                account_id = int(account_id_str)
            except ValueError:
                logger.error(f"Invalid account_id provided: {account_id_str}")
                return jsonify({'error': 'Invalid account_id format'}), 400

        if not symbols and not account_id:
             logger.error("Must provide either symbols or a valid account_id")
             return jsonify({'error': '请提供账户ID或股票列表'}), 400
        
        conn = get_db_connection()
        logger.info("数据库连接成功")
        
        # Flag to check if symbols were provided directly or derived
        symbols_provided_directly = bool(symbols) # True if symbols came from request.args
        
        # 如果没有指定股票，使用所有持仓股票
        if not symbols:
            logger.info(f"未指定股票，获取账户 {account_id} 的所有持仓股票")
            if not account_id:
                 # This case is already handled above, but double-check
                 logger.error("Logical error: No symbols and no account_id")
                 return jsonify({'error': '请提供账户ID或股票列表'}), 400
                
            positions_query = conn.execute('''
                SELECT DISTINCT symbol 
                FROM transactions 
                WHERE account_id = ?
                GROUP BY symbol 
                HAVING SUM(quantity) > 0 AND symbol != 'CASH'
            ''', [account_id]).fetchall()
            symbols = [row[0] for row in positions_query]
            logger.info(f"查询到的持仓股票: {symbols}")
            
        # Initialize metrics with default/empty values
        risk_metrics = {}
        performance_metrics = {}
        recommendations = []
        formatted_positions = []
        market_data = {}
        positions = []

        if not symbols:
            # Handle case where account exists but has no current stock holdings (only cash?)
            logger.warning(f"未找到账户 {account_id} 的任何持仓股票数据 (可能只有现金?)")
            # If no symbols derived from account_id, calculate performance for the account anyway
            if not symbols_provided_directly:
                 logger.info(f"No holdings, but calculating performance for account {account_id}")
                 performance_metrics = _get_performance_metrics(account_id) # Pass account_id here
            # Risk metrics remain empty as there are no positions to evaluate
        else:
            # 获取市场数据
            market_data = _get_market_data(symbols)
            
            # 获取持仓信息 (Pass account_id here too)
            positions = _get_positions(symbols, account_id)
            
            # Only calculate risk and performance if analyzing the whole portfolio
            if not symbols_provided_directly:
                # 计算风险指标
                risk_metrics = _calculate_risk_metrics(positions, market_data)
                
                # 获取绩效指标 (Pass account_id here)
                performance_metrics = _get_performance_metrics(account_id)
            else:
                 # Return default/empty values for risk and performance when specific symbols are requested
                 risk_metrics = {
                     "portfolio_var": 0.0,
                     "portfolio_vol": 0.0,
                     "max_drawdown": 0.0,
                     "sharpe_ratio": 0.0,
                     "concentration_risk": 0.0,
                     "risk_level": "N/A" # Indicate not applicable
                 }
                 performance_metrics = {
                     "annualized_return": 0,
                     "sharpe_ratio": 0,
                     "max_drawdown": 0,
                     "win_rate": 0,
                     "profit_loss_ratio": 0,
                     "max_gain_info": "N/A",
                     "max_loss_info": "N/A",
                     "avg_holding_days": 0
                 }
            # 格式化持仓数据 (Always format the positions found)
            formatted_positions = _format_positions(positions, market_data)

        # --- Ensure response structure is consistent ---
        if not risk_metrics: # Ensure risk_metrics is not empty if skipped
             risk_metrics = {
                 "portfolio_var": 0.0, "portfolio_vol": 0.0, "max_drawdown": 0.0,
                 "sharpe_ratio": 0.0, "concentration_risk": 0.0, "risk_level": "N/A"
             }
        if not performance_metrics: # Ensure performance_metrics is not empty if skipped
             performance_metrics = {
                 "annualized_return": 0, "sharpe_ratio": 0, "max_drawdown": 0, "win_rate": 0,
                 "profit_loss_ratio": 0, "max_gain_info": "N/A", "max_loss_info": "N/A", "avg_holding_days": 0
             }
        
        response_data = {
            "positions": formatted_positions,
            "risk_metrics": risk_metrics,
            "performance_metrics": performance_metrics,
            "recommendations": recommendations
        }
        
        logger.info("策略分析完成，准备返回数据")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"处理策略分析请求时发生错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({
            'error': str(e), 
            'message': '获取策略数据失败，请检查日志获取详细信息',
            'status': 'error'
        }), 500
    finally:
        if conn:
            conn.close()
            logger.info("数据库连接已关闭")

@strategies_bp.route("/technical-analysis", methods=['GET'], strict_slashes=False)
def get_technical_analysis():
    """获取股票的技术分析数据和交易点"""
    conn = None
    try:
        symbol = request.args.get('symbol')
        # Optional: Get account_id if transactions should be account-specific
        account_id = request.args.get('account_id') 
        logger.info(f"开始处理技术分析请求，股票代码: {symbol}, 账户ID: {account_id}") # Log account_id
        
        if not symbol:
            logger.warning("未提供股票代码")
            return jsonify({'error': 'Symbol is required'}), 400

        # 获取市场数据
        market_data = _get_market_data([symbol])
        
        if not market_data or symbol not in market_data or market_data[symbol].empty:
             logger.warning(f"{symbol} 未获取到有效市场数据")
             # Return empty data structure instead of 404 to allow chart rendering without data
             return jsonify({
                 'historicalData': [],
                 'indicators': {
                     'ma20': [], 
                     'ma50': [], 
                     'rsi': [],
                     'macd': {
                         'macd_line': [],
                         'signal_line': [],
                         'histogram': []
                     },
                     'ss_oscillator': []
                 },
                 'buy_points': [],
                 'sell_points': [],
                 'golden_cross_points': [],
                 'death_cross_points': [],
                 'second_golden_cross_points': []
             }), 200

        df = market_data[symbol]
        logger.info(f"获取到 {symbol} 的数据，形状: {df.shape}")
        
        # --- START: Daily Resampling Logic ---
        # Ensure the index is a DatetimeIndex
        if not isinstance(df.index, pd.DatetimeIndex):
            df.index = pd.to_datetime(df.index)
            
        # Define aggregation rules
        agg_rules = {
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }
        # Filter out columns not present in df to avoid errors during resampling
        existing_agg_rules = {col: rule for col, rule in agg_rules.items() if col in df.columns}
        
        daily_df = pd.DataFrame() # Initialize as an empty DataFrame
        
        if not existing_agg_rules:
            logger.warning(f"No standard OHLCV columns found in data for {symbol} for daily resampling. Original df shape: {df.shape}. daily_df will be empty, and no daily indicators will be generated.")
        else:
            logger.info(f"Standard OHLCV columns found for {symbol}. Resampling to daily.")
            try:
                daily_df = df.resample('D').agg(existing_agg_rules)
                daily_df.dropna(how='all', inplace=True) # Drop rows that are all NaN
                if not daily_df.empty:
                    daily_df.index = daily_df.index.normalize() # Ensure index is just date
                logger.info(f"Resampled {symbol} to daily data, new shape: {daily_df.shape}")
            except Exception as resample_err:
                logger.error(f"Error during resampling for {symbol}: {resample_err}. daily_df will be empty.")
                daily_df = pd.DataFrame() # Ensure it's empty on error

        if daily_df.empty:
            logger.warning(f"daily_df for {symbol} is empty after resampling attempt. Original df shape: {df.shape}. No daily technical indicators or historical data will be generated from daily_df.")
            # This state (empty daily_df) will lead to empty historicalData, ma20, ma50, rsi below.

        # 计算技术指标 (using daily_df from now on)
        logger.info("开始计算技术指标 (using daily_df if available)")
        historicalData = []
        dates = [] # Store dates for filtering transactions
        
        # Iterate over daily_df to build historicalData for the response
        if not daily_df.empty:
            # Helper to convert NaN to None for JSON compatibility
            def val_or_none(val):
                return None if pd.isna(val) else val

            for index, row in daily_df.iterrows():
                date_str = index.strftime('%Y-%m-%d')
                dates.append(date_str)
                # Ensure all expected keys are present, defaulting to None if not found or NaN
                historicalData.append({
                    'date': date_str,
                    'open': val_or_none(row.get('Open')), 
                    'high': val_or_none(row.get('High')),
                    'low': val_or_none(row.get('Low')),
                    'close': val_or_none(row.get('Close')),
                    'volume': val_or_none(row.get('Volume'))
                })
        logger.info(f"处理了 {len(historicalData)} 条每日历史数据 (from daily_df)")

        # 计算移动平均线 (use daily_df)
        logger.info("计算移动平均线 (from daily_df)")
        ma20 = []
        ma50 = []
        # Check if daily_df is not empty, has 'Close' column, and 'Close' column is not all NaNs
        if not daily_df.empty and 'Close' in daily_df.columns and not daily_df['Close'].dropna().empty:
            close_prices_filled = daily_df['Close'].ffill() # Forward fill for robust MA calculation
            if len(close_prices_filled) >= 20:
                ma20 = close_prices_filled.rolling(window=20).mean().tolist()
            else:
                logger.warning(f"Not enough data for 20-period MA for {symbol}. Got {len(close_prices_filled)} points.")
            if len(close_prices_filled) >= 50:
                ma50 = close_prices_filled.rolling(window=50).mean().tolist()
            else:
                logger.warning(f"Not enough data for 50-period MA for {symbol}. Got {len(close_prices_filled)} points.")
            logger.info(f"MA20最新值: {ma20[-1] if ma20 and pd.notna(ma20[-1]) else 'N/A'}, MA50最新值: {ma50[-1] if ma50 and pd.notna(ma50[-1]) else 'N/A'}")
        else:
            logger.warning(f"daily_df is empty, or no 'Close' data / all NaNs in 'Close' data for {symbol}. Cannot calculate MAs.")
        
        # 计算RSI (use daily_df with Wilder's method from utils)
        logger.info("计算RSI指标 (from daily_df using Wilder's method from utils)")
        rsi = [] # Default to empty list
        if not daily_df.empty and 'Close' in daily_df.columns and not daily_df['Close'].dropna().empty:
            close_prices_for_rsi = daily_df['Close'].dropna() # Use dropna to handle potential NaNs before calculation
            # Use the imported utility function
            rsi_calculated_series = calculate_wilder_rsi(close_prices_for_rsi)
            # Reindex to original daily_df index to align with other indicators, fillna for original NaNs
            rsi_calculated_series = rsi_calculated_series.reindex(daily_df.index)
            rsi = rsi_calculated_series.fillna(50).tolist() 
        else:
            logger.warning(f"daily_df is empty, or no 'Close' data / all NaNs in 'Close' data for {symbol}. Cannot calculate RSI.")

        # --- NEW: Calculate MACD and Crosses ---
        logger.info("计算MACD指标 (from daily_df)")
        macd_data = {
            'macd_line': [],
            'signal_line': [],
            'histogram': []
        }
        golden_cross_points = []
        death_cross_points = []
        second_golden_cross_points = []
        if not daily_df.empty and 'Close' in daily_df.columns and not daily_df['Close'].dropna().empty:
            close_prices_for_macd = daily_df['Close'].dropna()
            macd_df, golden_crosses, death_crosses, second_golden_crosses = calculate_macd(close_prices_for_macd)
            
            # Reindex to align with daily_df, filling NaNs for JSON compatibility
            macd_df = macd_df.reindex(daily_df.index).where(pd.notnull(macd_df), None)
            macd_data['macd_line'] = [None if pd.isna(v) else v for v in macd_df['MACD'].tolist()]
            macd_data['signal_line'] = [None if pd.isna(v) else v for v in macd_df['Signal_Line'].tolist()]
            macd_data['histogram'] = [None if pd.isna(v) else v for v in macd_df['MACD_Histogram'].tolist()]

            # Format cross points: [timestamp, price]
            for cross_date in golden_crosses:
                if cross_date in daily_df.index:
                    price = daily_df.loc[cross_date]['Close']
                    if pd.notna(price):
                        ts = int(cross_date.timestamp() * 1000)
                        golden_cross_points.append([ts, price])
            
            for cross_date in death_crosses:
                if cross_date in daily_df.index:
                    price = daily_df.loc[cross_date]['Close']
                    if pd.notna(price):
                        ts = int(cross_date.timestamp() * 1000)
                        death_cross_points.append([ts, price])
            
            # Format second golden cross points
            for cross_date in second_golden_crosses:
                if cross_date in daily_df.index:
                    price = daily_df.loc[cross_date]['Close']
                    if pd.notna(price):
                        ts = int(cross_date.timestamp() * 1000)
                        second_golden_cross_points.append([ts, price])
            
            logger.info(f"MACD 计算完成. 找到 {len(golden_cross_points)} 个金叉, {len(death_cross_points)} 个死叉, {len(second_golden_cross_points)} 个二次金叉.")
        else:
            logger.warning(f"无法计算MACD，因为 daily_df 为空或不包含'Close'数据。")
        # --- END: MACD Calculation ---

        # --- NEW: SuperSmoother Oscillator ---
        logger.info("计算SuperSmoother Oscillator (from daily_df)")
        ss_oscillator = []
        if not daily_df.empty and 'Close' in daily_df.columns and not daily_df['Close'].dropna().empty:
            close_prices_for_ss = daily_df['Close'].dropna()
            ss_short = calculate_supersmoother(close_prices_for_ss, 10)
            ss_long = calculate_supersmoother(close_prices_for_ss, 20)
            ss_osc_series = (ss_short - ss_long).reindex(daily_df.index)
            ss_oscillator = [None if pd.isna(v) else v for v in ss_osc_series.tolist()]
        else:
            logger.warning(f"无法计算SuperSmoother Oscillator，因为 daily_df 为空或不包含'Close'数据。")
        # --- END: SuperSmoother Oscillator ---

        # --- 获取买卖点 ---
        buy_points = []
        sell_points = []
        if dates: # Only query if we have dates from historical data
            conn = get_db_connection()
            min_date = dates[0]
            max_date = dates[-1]
            # Optional: Add account_id filter if needed
            account_filter = " AND account_id = ? " if account_id else ""
            params = [symbol, min_date, max_date]
            if account_id: 
                params.append(account_id) # Add account_id if it exists
            else: # If no account_id, we might need to adjust the query or handle differently
                 # For now, let's assume if no account_id, we show all trades for the symbol.
                 # If we *always* require an account_id for this endpoint, handle the error earlier.
                 pass 

            logger.info(f"查询 {symbol} 从 {min_date} 到 {max_date} 的交易记录 {('for account ' + str(account_id)) if account_id else ''}")
            # Add the account_filter placeholder dynamically using f-string
            query = f"""
                SELECT trans_time, price, quantity 
                FROM transactions 
                WHERE symbol = ? 
                  AND date(trans_time) >= ? 
                  AND date(trans_time) <= ?
                  {account_filter} -- Add filter here
                ORDER BY trans_time
            """
            try:
                # Parameters should now match the query string construction above
                logger.debug(f"Executing query with params: {params}")
                transactions = conn.execute(query, params).fetchall()
                logger.info(f"获取到 {len(transactions)} 条交易记录")

                for trans in transactions:
                    # Convert trans_time (likely datetime object) to 'YYYY-MM-DD' string for matching
                    trans_date_str = pd.to_datetime(trans['trans_time']).strftime('%Y-%m-%d') 
                    # Find the index in historical data corresponding to the transaction date
                    try:
                        # Find the index of the transaction date within the historical dates
                        # We need the index to align the transaction marker with the k-line data point
                        date_index = dates.index(trans_date_str) 
                        # We need the timestamp for the x-axis ('time')
                        trans_timestamp = pd.to_datetime(trans['trans_time']).timestamp() * 1000 # Echarts uses ms
                        point_data = {
                            # Use timestamp and price for coordinates
                            'coord': [trans_timestamp, trans['price']], 
                            'value': float(trans['quantity']), # Store quantity in value maybe? Or use for label
                            'itemStyle': { # Optional styling, will be overridden in frontend for consistency
                                'color': 'red' if trans['quantity'] < 0 else 'green' 
                            }
                        }
                        if trans['quantity'] > 0:
                            buy_points.append(point_data)
                        elif trans['quantity'] < 0:
                            sell_points.append(point_data)
                    except ValueError:
                         # Handle cases where transaction date is not in the historical data range (e.g., weekends, holidays)
                         logger.warning(f"Transaction date {trans_date_str} not found in historical data dates. Skipping point.")
                         continue # Skip this transaction point

            except Exception as db_err:
                logger.error(f"查询交易记录时出错: {db_err}")
                # Decide if we should return error or just empty points
                # Let's return empty points for now
                buy_points = []
                sell_points = []
        # --- 结束获取买卖点 ---

        response_data = {
            'historicalData': historicalData,
            'indicators': {
                'ma20': [v if pd.notna(v) else None for v in ma20], # Use pd.notna for checking NaN
                'ma50': [v if pd.notna(v) else None for v in ma50],
                'rsi': [v if pd.notna(v) else None for v in rsi],
                'macd': macd_data, # Add MACD data
                'ss_oscillator': ss_oscillator
            },
            'buy_points': buy_points, # Add buy points
            'sell_points': sell_points, # Add sell points
            'golden_cross_points': golden_cross_points, # Add golden crosses
            'death_cross_points': death_cross_points,   # Add death crosses
            'second_golden_cross_points': second_golden_cross_points  # Add second golden crosses
        }
        
        logger.info(f"技术分析数据处理完成，返回 {len(historicalData)} 条历史数据, {len(buy_points)} 买点, {len(sell_points)} 卖点")
        # logger.debug(f"返回数据: {response_data}") # Avoid logging potentially large data
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"获取技术分析数据时发生错误: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        # Return a generic error message and status
        return jsonify({'error': '获取技术分析数据失败', 'details': str(e)}), 500
    finally:
        if conn:
            conn.close()
            logger.info("数据库连接已关闭 (technical-analysis)")


def _get_market_data(symbols: List[str]) -> Dict[str, pd.DataFrame]:
    """从缓存获取市场数据"""
    logger.info(f"开始获取市场数据，股票列表: {symbols}")
    market_data = {}
    
    if not symbols:
        logger.warning("股票列表为空")
        return market_data
        
    for symbol in symbols:
        try:
            df = stock_cache.get_historical_data(symbol)
            
            if df is None:
                logger.warning(f"{symbol} 返回了None")
                continue
                
            if df.empty:
                logger.warning(f"{symbol} 返回空数据框")
                continue
                
            
            market_data[symbol] = df
            
        except Exception as e:
            logger.error(f"获取 {symbol} 数据时发生错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            
    logger.info(f"市场数据获取完成，成功获取 {len(market_data)} 个股票的数据")
    return market_data

def _get_positions(symbols: List[str], account_id: Optional[str] = None) -> List[Stock]:
    """获取实际持仓信息 (Corrected Query)"""
    conn = None
    positions = []
    try:
        conn = get_db_connection()
        
        # Use a query similar to calculate_holdings to get accurate current state
        # This query determines cost basis based on transactions since the last time the holding quantity was zero.
        holding_query = '''
            WITH date_filtered_transactions AS (
                -- Select all transactions for the relevant accounts/symbols
                SELECT * FROM transactions
                WHERE symbol = ? 
                AND account_id = COALESCE(?, account_id)
            ),
            cumulative_holdings AS (
                -- Calculate cumulative quantity over time to find zero-crossing points
                SELECT 
                    trans_time, 
                    symbol, 
                    account_id, 
                    SUM(quantity) OVER (PARTITION BY symbol, account_id ORDER BY trans_time, transaction_id) as cumulative_quantity
                FROM date_filtered_transactions
            ),
            last_zero_crossing AS (
                -- Find the latest time the cumulative quantity was <= 0 before the current holding period
                SELECT 
                    symbol, 
                    account_id, 
                    MAX(trans_time) as last_zero_time
                FROM cumulative_holdings
                WHERE cumulative_quantity <= 1e-9 -- Use tolerance for zero
                GROUP BY symbol, account_id
            ),
            relevant_buy_transactions AS (
                -- Select buy transactions that occurred *after* the last zero crossing
                SELECT 
                    t.symbol, 
                    t.account_id,
                    SUM(t.quantity) as relevant_buy_quantity, 
                    SUM(t.quantity * t.price) as relevant_total_cost
                FROM date_filtered_transactions t
                LEFT JOIN last_zero_crossing lzc ON t.symbol = lzc.symbol AND t.account_id = lzc.account_id
                WHERE t.quantity > 0 
                AND t.trans_time > COALESCE(lzc.last_zero_time, '1900-01-01 00:00:00') -- Only buys after the last zero time
                GROUP BY t.symbol, t.account_id
            ),
            current_holdings_calc AS (
                 -- Calculate the current net quantity across all transactions
                SELECT 
                    symbol,
                    account_id,
                    SUM(quantity) as current_quantity,
                    MIN(date(trans_time)) as first_entry_date -- Get the earliest transaction date overall
                FROM date_filtered_transactions
                GROUP BY symbol, account_id
            ),
            current_prices AS (
                -- Get the latest price for each symbol
                SELECT symbol, close as current_price
                FROM cached_prices
                WHERE symbol = ? 
                AND date = (SELECT MAX(date) FROM cached_prices WHERE symbol = ?)
                LIMIT 1
            )
            -- Final selection joining all parts
            SELECT 
                chc.symbol,
                chc.account_id,
                chc.current_quantity,
                chc.first_entry_date, -- Use the earliest transaction date as entry date
                cp.current_price,
                CASE 
                    WHEN COALESCE(rbt.relevant_buy_quantity, 0) > 1e-9 THEN
                        -- Calculate avg cost based ONLY on buys since last zero crossing
                        rbt.relevant_total_cost / rbt.relevant_buy_quantity
                    ELSE 0 -- Handle cases with no relevant buys (e.g., if holding came from split only)
                END as avg_cost_basis -- This is the correct average cost for the current holding
            FROM current_holdings_calc chc
            LEFT JOIN relevant_buy_transactions rbt 
                ON chc.symbol = rbt.symbol AND chc.account_id = rbt.account_id
            LEFT JOIN current_prices cp ON chc.symbol = cp.symbol
            WHERE chc.current_quantity > 1e-9 -- Ensure we only return current positive holdings
        '''
        
        for symbol in symbols:
            position_data = conn.execute(holding_query, [symbol, account_id, symbol, symbol]).fetchone()

            if position_data:
                pos_dict = dict(position_data)

                # --- Calculate Volatility and Beta --- 
                df = stock_cache.get_historical_data(symbol)
                volatility = 0.0
                beta = 1.0
                if not df.empty:
                    returns = df['Close'].pct_change().dropna()
                    volatility = returns.std() * np.sqrt(252)
                    try:
                        market_df = stock_cache.get_historical_data('SPY')
                        market_returns = market_df['Close'].pct_change().dropna()
                        common_index = returns.index.intersection(market_returns.index)
                        returns_aligned = returns.loc[common_index]
                        market_returns_aligned = market_returns.loc[common_index]
                        if len(returns_aligned) > 1 and len(market_returns_aligned) > 1:
                             covariance = np.cov(returns_aligned, market_returns_aligned)[0][1]
                             market_variance = np.var(market_returns_aligned)
                             beta = (covariance / market_variance) if market_variance != 0 else 1.0
                        else:
                             logger.warning(f"Not enough aligned data to calculate beta for {symbol}.")
                    except Exception as e_beta:
                        logger.warning(f"计算 {symbol} 的beta时出错: {e_beta}，使用默认值1.0")
                else:
                    logger.warning(f"{symbol} 无历史数据，使用默认波动率和beta")

                # Create the Stock object with the CORRECTED data
                stock_obj = Stock(
                    symbol=symbol,
                    shares=pos_dict['current_quantity'], # Correct current quantity
                    avg_price=pos_dict['avg_cost_basis'], # Correct average cost basis
                    entry_date=pd.to_datetime(pos_dict['first_entry_date']), # Use first entry date 
                    volatility=volatility,
                    beta=beta,
                    current_price=pos_dict.get('current_price') # Pass current price fetched
                )
                
                # --- Update PnL using the Stock object's method (which uses the passed current_price) ---
                # Note: update_risk_metrics now mostly just updates internal PnL state based on the already passed current_price
                # and potentially vol/beta if market_data (df) was available.
                if stock_obj.current_price is not None:
                    try:
                        stock_obj.update_risk_metrics(stock_obj.current_price, df)
                    except Exception as e_pnl:
                         logger.error(f"Error updating PnL for {symbol} via update_risk_metrics: {e_pnl}")
                else:
                    logger.warning(f"Could not update PnL for {symbol} due to missing current price.")

                positions.append(stock_obj)
            else:
                logger.warning(f"No current holding data found for {symbol} after query.")

    except Exception as e:
        logger.opt(exception=True).error(f"获取持仓信息时发生严重错误: {str(e)}")
    finally:
        if conn:
            conn.close()
    
    return positions

def _format_positions(positions: List[Stock], market_data: dict) -> List[dict]:
    """Format position data for frontend display"""
    try:
        formatted_positions = []
        for pos in positions:
            # Access PnL data directly from the Stock object properties
            # The update_risk_metrics call in _get_positions should have set these
            current_pos_value = pos.current_value # Use the property
            unrealized_pnl_val = pos.unrealized_pnl # Use the property
            # Calculate PnL percentage safely
            total_cost_basis = pos.shares * pos.avg_price
            unrealized_pnl_pct_val = (unrealized_pnl_val / total_cost_basis) if total_cost_basis != 0 else 0

            formatted_position = {
                'symbol': pos.symbol,
                'quantity': float(pos.shares),
                'avg_price': float(pos.avg_price),
                'current_price': float(pos.current_price) if pos.current_price is not None else None, # Get from Stock obj if available
                'current_value': float(current_pos_value),
                'unrealized_pnl': float(unrealized_pnl_val),
                'unrealized_pnl_pct': float(unrealized_pnl_pct_val), # Use calculated percentage
                'entry_date': pos.entry_date.strftime('%Y-%m-%d') if pos.entry_date else None,
                'volatility': float(pos.volatility * 100) if pos.volatility is not None else 0.0, # Convert to percentage
                'beta': float(pos.beta) if pos.beta is not None else 1.0,
                'rsi': None, # Placeholder - calculate below if needed
            }

            # Add technical indicators if market data exists
            if pos.symbol in market_data:
                df = market_data[pos.symbol].copy()
                df.columns = df.columns.str.title()
                if not isinstance(df.index, pd.DatetimeIndex):
                    df.index = pd.to_datetime(df.index)
                
                # Calculate RSI if 'Close' data is available using the utility function
                if 'Close' in df.columns and not df['Close'].empty:
                    try:
                        close_prices_for_rsi_pos = df['Close'].dropna()
                        period_rsi_pos = 14
                        if len(close_prices_for_rsi_pos) > period_rsi_pos:
                            # Use the imported utility function
                            rsi_series = calculate_wilder_rsi(close_prices_for_rsi_pos, period=period_rsi_pos)
                            if not rsi_series.empty and not rsi_series.dropna().empty:
                                formatted_position['rsi'] = round(float(rsi_series.dropna().iloc[-1]), 2)
                            else:
                                formatted_position['rsi'] = None # Default if RSI calculation is problematic or all NaN
                        else:
                            logger.warning(f"Not enough data points for RSI for {pos.symbol} in _format_positions. Got {len(close_prices_for_rsi_pos)}")
                            formatted_position['rsi'] = None
                    except Exception as e:
                        logger.warning(f"Error calculating RSI for {pos.symbol} in _format_positions: {e}")
                        formatted_position['rsi'] = None # Default on error
                else:
                    formatted_position['rsi'] = None # Default if no 'Close' data

            formatted_positions.append(formatted_position)

        return formatted_positions
        
    except Exception as e:
        logger.opt(exception=True).error(f"格式化持仓数据时发生错误: {str(e)}")
        return []

def _calculate_risk_metrics(positions: List[Stock], market_data: Dict[str, pd.DataFrame]) -> Dict:
    """Calculate portfolio risk metrics"""
    logger.info("开始计算风险指标")
    try:
        # 检查持仓是否为空
        if not positions:
            logger.warning("没有持仓，返回默认风险指标")
            return {
                "portfolio_var": 0.0,
                "portfolio_vol": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "concentration_risk": 0.0,
                "risk_level": "LOW"
            }
            
        valid_data_count = sum(1 for pos in positions if pos.symbol in market_data)
        logger.info(f"有效市场数据数量: {valid_data_count}")
        
        # 计算总持仓价值
        total_value = sum(pos.current_value for pos in positions)
        logger.info(f"总持仓价值: {total_value}")
        
        if total_value <= 0:
            logger.warning("持仓总价值为0或负数，返回默认风险指标")
            return {
                "portfolio_var": 0.0,
                "portfolio_vol": 0.0,
                "max_drawdown": 0.0,
                "sharpe_ratio": 0.0,
                "concentration_risk": 0.0,
                "risk_level": "LOW"
            }
        
        # 更新每个持仓的风险指标
        for pos in positions:
            if pos.symbol in market_data:
                df = market_data[pos.symbol].copy()
                if not df.empty and 'Close' in df.columns:
                    try:
                        pos.update_risk_metrics(df['Close'].iloc[-1], df)
                    except Exception as e:
                        logger.error(f"更新 {pos.symbol} 风险指标时出错: {str(e)}")
                        # Set default values if update fails
                        pos.volatility = 0.0
                        pos.beta = 1.0
            else:
                logger.warning(f"{pos.symbol} 没有市场数据")
                # Set default values for missing data
                pos.volatility = 0.0
                pos.beta = 1.0
        
        # 调用风险管理器计算风险指标
        logger.info("调用风险管理器计算组合风险指标")
        risk_metrics_output = {} # Initialize to ensure it's always a dict
        try:
            # Call RiskManager
            risk_metrics_candidate = RiskManager.calculate_portfolio_risk(
                positions=positions,
                market_data=market_data,
                lookback_window=252  # 使用1年历史数据
            )
            logger.info(f"风险指标计算结果 (raw from RiskManager): {risk_metrics_candidate}")

            if isinstance(risk_metrics_candidate, dict):
                risk_metrics_output = risk_metrics_candidate # Use if it's a dict
            else:
                logger.warning(f"RiskManager did not return a dict (got {type(risk_metrics_candidate)}). Using empty dict for further processing.")
                # risk_metrics_output remains {}
        except Exception as e_rm_call:
            logger.error(f"Call to RiskManager.calculate_portfolio_risk failed: {str(e_rm_call)}. Using empty dict for further processing.")
            # risk_metrics_output remains {}
        
        # 确保所有风险指标都是有效的数值
        risk_metrics = {
            k: 0.0 if pd.isna(v) or np.isinf(v) else float(v)
            for k, v in risk_metrics_output.items() # Iterate over the safe risk_metrics_output
            if k != 'risk_level'  # 不处理risk_level，因为它是字符串
        }
        
        # 添加额外的风险指标
        try:
            # 计算集中度风险（最大单一持仓占比）
            max_position = max(pos.current_value for pos in positions)
            concentration_risk = (max_position / total_value)  # 保持为小数形式
            risk_metrics['concentration_risk'] = 0.0 if pd.isna(concentration_risk) else float(concentration_risk)
            
            logger.info(f"集中度风险计算 - 最大持仓: {max_position:.2f}, 总价值: {total_value:.2f}, 集中度: {concentration_risk:.4f}")
            
            # 计算最大回撤
            portfolio_values = pd.Series()
            for pos in positions:
                if pos.symbol in market_data:
                    df = market_data[pos.symbol].copy()  # 创建副本以避免修改原始数据
                    close_prices = df['Close']
                    
                    # 处理时区问题 - 确保所有DatetimeIndex都是tz-naive
                    if hasattr(close_prices.index, 'tz') and close_prices.index.tz is not None:
                        close_prices.index = close_prices.index.tz_localize(None)
                    
                    # 如果portfolio_values已有时区信息，确保与close_prices匹配
                    if not portfolio_values.empty and hasattr(portfolio_values.index, 'tz'):
                        if portfolio_values.index.tz is not None and (not hasattr(close_prices.index, 'tz') or close_prices.index.tz is None):
                            portfolio_values.index = portfolio_values.index.tz_localize(None)
                    
                    position_values = close_prices * pos.shares
                    
                    # 使用安全的方法合并序列，处理索引不匹配的情况
                    try:
                        if portfolio_values.empty:
                            portfolio_values = position_values
                        else:
                            # 找到共同的日期索引
                            common_index = portfolio_values.index.intersection(position_values.index)
                            
                            # 如果没有共同索引，则跳过这个股票
                            if len(common_index) == 0:
                                continue
                            
                            # 重新索引到共同日期
                            portfolio_subset = portfolio_values.loc[common_index]
                            position_subset = position_values.loc[common_index]
                            
                            # 安全地相加
                            portfolio_values = portfolio_subset.add(position_subset)
                    except Exception as e:
                        logger.warning(f"处理股票 {pos.symbol} 时发生错误: {str(e)}，跳过该股票")
                        continue
            
            if not portfolio_values.empty:
                rolling_max = portfolio_values.expanding().max()
                drawdowns = (portfolio_values - rolling_max) / rolling_max
                max_drawdown = abs(min(drawdowns.min(), 0.0)) if not drawdowns.empty else 0.0
                max_drawdown = min(max_drawdown, 1.0)  # 确保不超过100%
                risk_metrics['max_drawdown'] = float(max_drawdown)
                
                logger.info(f"最大回撤计算结果: {max_drawdown:.4f}")
            else:
                risk_metrics['max_drawdown'] = 0.0
                
        except Exception as e:
            logger.error(f"计算集中度风险和最大回撤时出错: {str(e)}")
            risk_metrics['concentration_risk'] = 0.0
            risk_metrics['max_drawdown'] = 0.0
        
        # 确保包含所有必要的风险指标
        default_metrics = {
            "portfolio_var": 0.0,
            "portfolio_vol": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "concentration_risk": 0.0
        }
        
        # 合并计算的指标和默认值
        risk_metrics = {**default_metrics, **{k: v for k, v in risk_metrics.items() if k != 'risk_level'}}
        
        # 计算风险指标
        portfolio_var = risk_metrics['portfolio_var']
        portfolio_vol = risk_metrics['portfolio_vol']  # 已经是小数形式
        max_drawdown = risk_metrics['max_drawdown']
        sharpe_ratio = risk_metrics['sharpe_ratio']
        concentration_risk = risk_metrics['concentration_risk']
        
        # 确保值在合理范围内
        portfolio_vol = min(max(0, portfolio_vol), 1.0)  # 限制在0-100%之间
        max_drawdown = min(max(0, max_drawdown), 1.0)  # 限制在0-100%之间
        concentration_risk = min(max(0, concentration_risk), 1.0)  # 限制在0-100%之间
        
        # 根据portfolio_vol判断risk_level
        if portfolio_vol > 0.25:  # 25%
            risk_level = "HIGH"
        elif portfolio_vol > 0.15:  # 15%
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        risk_metrics = {
            "portfolio_var": float(portfolio_var * 100),  # 转换为百分比
            "portfolio_vol": float(portfolio_vol * 100),  # 转换为百分比
            "max_drawdown": float(max_drawdown * 100),  # 转换为百分比
            "sharpe_ratio": float(sharpe_ratio),  # 保持原值
            "concentration_risk": float(concentration_risk * 100),  # 转换为百分比
            "risk_level": risk_level
        }
        
        logger.info(f"最终风险指标: {risk_metrics}")
        return risk_metrics
    except Exception as e:
        logger.error(f"计算风险指标时发生错误: {str(e)}")
        return {
            "portfolio_var": 0.0,
            "portfolio_vol": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "concentration_risk": 0.0,
            "risk_level": "LOW"
        }

def _get_performance_metrics(account_id=None) -> Dict:
    """Get portfolio performance metrics based on historical data for a specific account."""
    conn = None
    try:
        conn = get_db_connection()
        
        # --- Fetch Transactions for the specified account ---
        account_filter = f"WHERE account_id = {account_id}" if account_id else ""
        trades_query = f'''
            SELECT symbol, quantity, price, trans_time
            FROM transactions
            {account_filter} 
            AND symbol != 'CASH'
            ORDER BY trans_time
        '''
        trades = pd.read_sql(trades_query, conn)
        
        if trades.empty:
            logger.warning(f"No non-CASH transactions found for account {account_id} to calculate performance metrics.")
            # Return default values if no trades
            return {
                "annualized_return": 0,
                "sharpe_ratio": 0,
                "max_drawdown": 0,
                "win_rate": 0,
                "profit_loss_ratio": 0,
                "max_gain_info": "N/A, 0.00%, N/A",
                "max_loss_info": "N/A, 0.00, N/A",
                "avg_holding_days": 0
            }
        
        # --- Continue with calculations using the filtered 'trades' DataFrame ---
        trades['trans_time'] = pd.to_datetime(trades['trans_time'])
        trades['value'] = trades['quantity'] * trades['price']
        trades['cumulative_quantity'] = trades.groupby('symbol')['quantity'].cumsum()
        trades['cumulative_value'] = trades.groupby('symbol')['value'].cumsum()
        
        # Calculate average holding period (remains the same logic)
        position_duration = trades.groupby('symbol').agg({
            'trans_time': lambda x: (x.max() - x.min()).days
        })
        avg_holding_period = position_duration['trans_time'].mean() if not position_duration.empty else 0
        
        # Calculate win rate and profit/loss ratio (remains the same logic)
        profitable_trades = trades[trades['value'] > 0].shape[0]
        total_trades = trades.shape[0]
        win_rate = profitable_trades / total_trades * 100 if total_trades > 0 else 0
        
        gains = trades[trades['value'] > 0]['value'].sum()
        losses = abs(trades[trades['value'] < 0]['value'].sum())
        profit_loss_ratio = gains / losses if losses != 0 else 0
        
        # --- Max Gain/Loss Calculation (Filter by account_id) ---
        max_gain_info_str = "N/A, 0.00%, N/A"
        try:
            gain_account_filter = f"AND account_id = {account_id}" if account_id else ""
            gain_query = f'''
                SELECT symbol, sell_date, MAX(((sell_price - buy_price) * 100.0) / buy_price) as max_pct_gain
                FROM realized_gains
                WHERE buy_price > 0 {gain_account_filter}
                GROUP BY account_id, symbol -- Ensure MAX is per symbol within account
                ORDER BY max_pct_gain DESC
                LIMIT 1
            '''
            cursor = conn.execute(gain_query)
            max_gain_query = cursor.fetchone()

            if max_gain_query and max_gain_query['max_pct_gain'] is not None:
                mg_symbol = max_gain_query['symbol']
                mg_pct = round(max_gain_query['max_pct_gain'], 2)
                mg_date = max_gain_query['sell_date']
                try:
                    mg_date_formatted = pd.to_datetime(mg_date).strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                     mg_date_formatted = str(mg_date)
                max_gain_info_str = f"{mg_symbol}, {mg_pct}%, {mg_date_formatted}"
                logger.info(f"Max Gain found for account {account_id}: {max_gain_info_str}")
            else:
                 logger.info(f"No realized gains found for account {account_id} or max gain calculation failed.")

        except Exception as gain_err:
            logger.exception(f"Error querying/calculating max gain from realized_gains for account {account_id}: {gain_err}")

        max_loss_info_str = "N/A, 0.00, N/A"
        try:
            loss_account_filter = f"AND account_id = {account_id}" if account_id else ""
            loss_query = f'''
                SELECT symbol, sell_date, MIN(realized_gain) as max_monetary_loss
                FROM realized_gains
                WHERE realized_gain < 0 {loss_account_filter}
                GROUP BY account_id, symbol -- Ensure MIN is per symbol within account
                ORDER BY max_monetary_loss ASC
                LIMIT 1
            '''
            cursor = conn.execute(loss_query)
            max_loss_query = cursor.fetchone()

            if max_loss_query and max_loss_query['max_monetary_loss'] is not None:
                ml_symbol = max_loss_query['symbol']
                ml_loss_value = round(max_loss_query['max_monetary_loss'], 2)
                ml_date = max_loss_query['sell_date']
                try:
                    ml_date_formatted = pd.to_datetime(ml_date).strftime('%Y-%m-%d')
                except (ValueError, TypeError):
                     ml_date_formatted = str(ml_date)
                max_loss_info_str = f"{ml_symbol}, {ml_loss_value:.2f}, {ml_date_formatted}"
                logger.info(f"Largest realized monetary loss found for account {account_id}: {max_loss_info_str}")
            else:
                 logger.info(f"No realized losses found in realized_gains table for account {account_id}.")

        except Exception as loss_err:
            logger.error(f"Error querying/calculating max loss from realized_gains for account {account_id}: {loss_err}")

        # --- Calculate Annualized Return using TWR for the specific account ---
        annualized_return = calculate_twr(conn, account_id=account_id) # Pass account_id here
        logger.info(f"Calculated Annualized TWR for account {account_id}: {annualized_return}")
        
        # Max Drawdown Calculation - Placeholder
        max_drawdown = 0.0 # Placeholder, actual calculation needs daily portfolio values
        
        # Calculate Sharpe ratio (based on transaction values, might be inaccurate)
        returns = trades['value'].pct_change().dropna()
        sharpe_ratio = 0.0
        if not returns.empty and returns.std() != 0:
            sharpe_ratio = np.sqrt(252) * (returns.mean() / returns.std())
        
        return {
            "annualized_return": annualized_return, 
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown, # Added placeholder
            "win_rate": round(win_rate, 2),
            "profit_loss_ratio": round(profit_loss_ratio, 2),
            "max_gain_info": max_gain_info_str, # Use corrected string
            "max_loss_info": max_loss_info_str, # Use corrected string
            "avg_holding_days": int(avg_holding_period) if not pd.isna(avg_holding_period) else 0 # Handle potential NaN
        }
    except Exception as e:
        logger.exception(f"计算绩效指标时发生错误 for account {account_id}: {str(e)}")
        # Return default values on error
        return {
            "annualized_return": 0,
            "sharpe_ratio": 0,
            "max_drawdown": 0,
            "win_rate": 0,
            "profit_loss_ratio": 0,
            "max_gain_info": "N/A, 0.00%, N/A",
            "max_loss_info": "N/A, 0.00, N/A",
            "avg_holding_days": 0
        }
    finally:
        if conn:
            conn.close()
            logger.info(f"Database connection closed for _get_performance_metrics (account: {account_id})")

@strategies_bp.route("/detailed-recommendation", methods=['POST'], strict_slashes=False)
def detailed_recommendation():
    """获取单个股票的详细AI建议"""
    try:
        data = request.get_json()
        symbol = data.get('symbol')
        position_data = data.get('position_data', {})
        account_id = data.get('account_id')
        
        logger.info(f"详细建议请求 - 股票: {symbol}, 账户: {account_id}")
        
        # --- 实现获取用户投资组合摘要的逻辑 ---
        portfolio_summary = {
            "total_value": "N/A",
            "top_sectors": "N/A",
            "risk_profile": "中等" # 假设为中等，未来可从用户信息中获取
        }
        if account_id:
            try:
                conn = get_db_connection()
                # 1. 获取投资组合总价值
                current_value, _, _ = calculate_current_market_value(conn, account_id=account_id)
                portfolio_summary["total_value"] = f"${current_value:,.2f}"

                # 2. 获取持仓以确定行业分布
                holdings_query = 'SELECT DISTINCT symbol FROM transactions WHERE account_id = ? AND symbol != "CASH"'
                holdings = conn.execute(holdings_query, [account_id]).fetchall()
                symbols_in_portfolio = [row['symbol'] for row in holdings]

                # 3. 获取各持仓的行业信息
                sectors = []
                for holding_symbol in symbols_in_portfolio:
                    try:
                        ticker_info = yf.Ticker(holding_symbol).info
                        if 'sector' in ticker_info:
                            sectors.append(ticker_info['sector'])
                    except Exception:
                        logger.warning(f"无法获取股票 {holding_symbol} 的行业信息。")
                
                if sectors:
                    sector_counts = pd.Series(sectors).value_counts()
                    top_sectors = sector_counts.nlargest(3).index.tolist()
                    portfolio_summary["top_sectors"] = ", ".join(top_sectors)
                
            except Exception as e:
                logger.error(f"获取账户 {account_id} 的投资组合摘要时出错: {e}")
            finally:
                if 'conn' in locals() and conn:
                    conn.close()
        # --- 结束投资组合摘要逻辑 ---

        # 构建提示词
        prompt = f"""作为一名顶级的、以基本面分析为主导的股票分析师，请为一位**长期投资**爱好者对以下股票进行详细、客观的分析，并提供个性化的投资建议。

---
**第一部分：全面的市场与个人背景**

1.  **宏观与市场情绪**:
    -   当前市场情绪
    -   标普500 YTD回报率
    -   纳斯达克100 YTD回报率
    -   利率环境
    *请在分析中务必考虑这些宏观因素对 {symbol} 的潜在影响。*

2.  **投资者个人情况**:
    -   投资组合概览: 价值 {portfolio_summary.get("total_value", "N/A")}, 主要分布在 {portfolio_summary.get("top_sectors", "N/A")} 等行业。
    -   风险偏好: {portfolio_summary.get("risk_profile", "N/A")}
    *请结合投资者的现有情况和风险偏好，给出更具个性化的建议，例如该投资是否会加剧集中度风险，或是否符合其风险承受能力。*

---
**第二部分：目标公司深入分析**

1.  **股票核心信息**:
    -   代码: {symbol}
    -   当前价格: ${position_data.get('current_price', 'N/A')}
    -   持仓成本: ${position_data.get('avg_price', 'N/A')} {{ '(持仓中)' if position_data.get('quantity', 0) > 0 else '' }}
    -   持仓数量: {position_data.get('quantity', 'N/A')} {{ '(持仓中)' if position_data.get('quantity', 0) > 0 else '' }}
    -   RSI (14日): {position_data.get('rsi', 'N/A')}
    
2.  **公司基本面信息 (源: yfinance)**: {yf.Ticker(symbol).info} 
    (请**深入挖掘并穿透分析**此信息中的关键基本面数据，**重点关注**:
    - **商业模式与战略**: `businessSummary`, `longBusinessSummary` (如有)。
    - **行业与市场**: `industry`, `sector`, `marketCap`, `fullTimeEmployees`。
    - **管理层与治理**: `companyOfficers` (评估高管背景与经验)，`auditRisk`, `boardRisk`, `compensationRisk`, `shareHolderRightsRisk` (评估治理结构)。
    - **核心财务数据**: 从 `financialData` 中提取并分析 `currentRatio`, `quickRatio`, `debtToEquity`, `returnOnAssets`, `returnOnEquity`, `revenueGrowth`, `earningsGrowth`, `totalRevenue`, `grossProfits`, `ebitda`, `freeCashflow` (若有)。特别关注**利润率指标** (`grossMargins`, `ebitdaMargins`, `profitMargins`) 的**水平和趋势**。
    - **关键估值统计**: 从 `defaultKeyStatistics` 或 `summaryDetail` 中提取并分析 `enterpriseValue`, `enterpriseToRevenue`, `enterpriseToEbitda`, `pegRatio`, `priceToBook`, `forwardPE`, `trailingPE`, `dividendYield`, `forwardEps`, `trailingEps`。**注意比较历史和行业水平**。
    - **近期动态**: 关注 `financialsTemplate` (年度/季度报告), `calendarEvents` (财报日期), 以及任何近期重大新闻或分析师评级变动 (`recommendationKey`, `recommendationsSummary` (如有))。
    **不要仅仅罗列数据，要提炼出对投资决策有价值的洞见。**)

---
**第三部分：输出要求**

请严格按照以下JSON格式提供分析:
{{
  "summary": "针对 {symbol} 的长期投资价值进行清晰总结，明确给出核心投资建议 (例如：增持、持有、减持/卖出、或保持观察)。说明此建议的主要依据。",
  "reasons": [
      "列出3-5个支持您核心建议的**关键理由**，优先侧重于公司的基本面、竞争优势、增长前景和当前估值水平。",
      "理由2...",
      "理由3..."
  ],
  "action_plan": [
    {{
      "title": "行动步骤1 (例如：买入/增持策略)",
      "description": "如果建议买入/增持，详细说明建议的买入价格区间、仓位管理建议 (例如：占总投资组合的百分比) 以及买入时间点的考虑因素 (例如：分批买入、等待技术回调)。"
    }},
    {{
      "title": "行动步骤2 (例如：持有/卖出策略)",
      "description": "如果建议持有或卖出，说明理由和关键的触发条件或观察信号。"
    }},
    {{
      "title": "行动步骤3 (例如：关键监控指标)",
      "description": "列出需要长期跟踪的关键财务指标、业务进展或宏观因素，以验证或调整投资逻辑。"
    }}
    // 根据实际情况提供2-3个最相关的步骤
  ],
  "analysis": {{
      "fundamental": "深入分析公司的**基本面**：评估其业务模式的可持续性、护城河（竞争优势）、管理层质量、财务健康状况 (关注盈利质量、现金流、债务水平)、关键估值指标及其历史和行业比较。",
      "technical": "简要分析当前**技术面**状态 (结合提供的RSI、趋势强度等)，并说明其对**长期**投资策略的潜在参考意义 (例如：当前价格是否处于相对有利的长期介入区间？是否存在明显的技术风险信号？)。避免过度关注短期波动。",
   }},
  "risks": [
      "明确列出投资 {symbol} 的2-4个主要**风险点**，并简要说明其潜在影响 (例如：行业周期性风险、竞争加剧风险、公司特有经营风险、宏观经济风险、估值过高风险)。",
      "风险2...",
      "风险3..."
  ]
}}

请确保分析客观、深入，论据充分，并始终紧密围绕用户的**长期投资**目标。语言专业、严谨。避免使用模糊不清或过于乐观/悲观的表述。
"""
        
        # 调用LLM API
        logger.info(f"开始调用LLM API获取{symbol}的详细建议")
        recommendation = llm_service.call_llm_api_json(prompt)
        logger.info("LLM API调用完成，准备处理响应")
        
        # 检查响应是否为空或无效
        if not recommendation:
            logger.warning(f"LLM返回了空的JSON对象，为{symbol}创建默认响应")
            recommendation = {
                "summary": f"对{symbol}的分析暂时无法完成。",
                "reasons": ["分析服务目前不可用"],
                "action_plan": [
                    {
                        "title": "稍后重试",
                        "description": "请稍后再尝试获取此股票的分析"
                    }
                ],
                "analysis": "无法提供详细分析。"
            }
        
        return jsonify(recommendation)
    
    except Exception as e:
        logger.error(f"获取股票详细建议时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/get_model_future_predictions', methods=['GET'])
def get_model_future_predictions():
    """获取模型预测的未来价格"""
    try:
        symbol = request.args.get('symbol')
        if not symbol:
            logger.error("Symbol parameter is missing")
            return jsonify({"error": "Symbol parameter is required"}), 400

        future_predictions = get_future_predictions_for_ticker(symbol)
        return jsonify(future_predictions)
    except Exception as e:
        logger.error(f"获取模型预测的未来价格时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500


# ============================================================================
# OPTIONS ANALYSIS ENDPOINTS
# ============================================================================

@strategies_bp.route('/options/watchlists', methods=['GET'])
def get_user_watchlists():
    """获取用户的期权分析观察列表"""
    try:
        account_id = request.args.get('account_id', type=int)
        if not account_id:
            return jsonify({"error": "Account ID is required"}), 400

        watchlists = options_service.get_user_watchlist(account_id)
        return jsonify({
            "watchlists": watchlists,
            "status": "success"
        })
    except Exception as e:
        logger.error(f"获取观察列表时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/watchlists', methods=['POST'])
def create_or_update_watchlist():
    """创建或更新观察列表"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')
        symbols = data.get('symbols', [])
        name = data.get('name', 'Default Watchlist')
        watchlist_id = data.get('watchlist_id')

        if not account_id:
            return jsonify({"error": "Account ID is required"}), 400

        if not symbols:
            return jsonify({"error": "Symbols list cannot be empty"}), 400

        watchlist_id = options_service.create_or_update_watchlist(
            account_id, symbols, name, watchlist_id
        )

        return jsonify({
            "watchlist_id": watchlist_id,
            "message": "Watchlist saved successfully",
            "status": "success"
        })
    except Exception as e:
        logger.error(f"保存观察列表时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/config/<strategy_type>', methods=['GET'])
def get_strategy_config(strategy_type):
    """获取策略配置"""
    try:
        account_id = request.args.get('account_id', type=int)
        if not account_id:
            return jsonify({"error": "Account ID is required"}), 400

        valid_strategies = ['cash_secured_puts', 'covered_calls', 'iron_condors']
        if strategy_type not in valid_strategies:
            return jsonify({"error": f"Invalid strategy type. Must be one of: {valid_strategies}"}), 400

        config = options_service.get_strategy_config(account_id, strategy_type)
        return jsonify({
            "config": config,
            "strategy_type": strategy_type,
            "status": "success"
        })
    except Exception as e:
        logger.error(f"获取策略配置时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/config/<strategy_type>', methods=['POST'])
def save_strategy_config(strategy_type):
    """保存策略配置"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')
        config_data = data.get('config', {})
        is_default = data.get('is_default', False)

        if not account_id:
            return jsonify({"error": "Account ID is required"}), 400

        valid_strategies = ['cash_secured_puts', 'covered_calls', 'iron_condors']
        if strategy_type not in valid_strategies:
            return jsonify({"error": f"Invalid strategy type. Must be one of: {valid_strategies}"}), 400

        config_id = options_service.save_strategy_config(
            account_id, strategy_type, config_data, is_default
        )

        return jsonify({
            "config_id": config_id,
            "message": "Strategy configuration saved successfully",
            "status": "success"
        })
    except Exception as e:
        logger.error(f"保存策略配置时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/analyze', methods=['POST'])
def analyze_options_strategies():
    """执行期权策略分析"""
    try:
        data = request.get_json()
        account_id = data.get('account_id')
        strategy_type = data.get('strategy_type')
        symbols = data.get('symbols', [])
        custom_config = data.get('config')

        if not account_id:
            return jsonify({"error": "Account ID is required"}), 400

        if not strategy_type:
            return jsonify({"error": "Strategy type is required"}), 400

        if not symbols:
            return jsonify({"error": "Symbols list cannot be empty"}), 400

        valid_strategies = ['cash_secured_puts', 'covered_calls', 'iron_condors']
        if strategy_type not in valid_strategies:
            return jsonify({"error": f"Invalid strategy type. Must be one of: {valid_strategies}"}), 400

        # Get configuration
        if custom_config:
            config = custom_config
        else:
            config = options_service.get_strategy_config(account_id, strategy_type)

        logger.info(f"开始分析 {strategy_type} 策略，股票: {symbols}")

        # Fetch options and price data
        options_df, prices = options_data_manager.fetch_data_for_batch(symbols, {
            'data_acquisition': {
                'batch_size': 10,
                'use_cache': True
            }
        })

        if options_df.empty:
            # Provide more detailed error information
            error_details = {
                "error": "No options data available for the specified symbols",
                "symbols_requested": symbols,
                "possible_reasons": [
                    "Symbols may not support options trading",
                    "Network connectivity issues with data provider",
                    "Invalid or delisted symbols",
                    "API rate limiting or temporary service issues"
                ],
                "suggestions": [
                    "Verify symbols are valid and actively traded",
                    "Try well-known symbols like AAPL, MSFT, or TSLA",
                    "Check if symbols are ETFs or leveraged products (may have limited options)",
                    "Wait a few minutes and try again if experiencing connectivity issues"
                ],
                "candidates": [],
                "market_conditions": {},
                "status": "error"
            }

            # Add symbol validation information if available
            try:
                options_manager = OptionsDataManager()
                validation_info = {}
                for symbol in symbols:
                    validation_info[symbol] = options_manager.validate_symbol(symbol)
                error_details["symbol_validation"] = validation_info
            except Exception as e:
                logger.warning(f"Could not add validation info: {e}")

            return jsonify(error_details), 400

        # Get market conditions
        market_conditions = options_data_manager.get_market_conditions(symbols)

        # Analyze based on strategy type
        candidates = pd.DataFrame()

        if strategy_type == 'cash_secured_puts':
            candidates = options_analyzer.find_cash_secured_put_candidates(options_df, prices, config)
        elif strategy_type == 'covered_calls':
            candidates = options_analyzer.find_covered_call_candidates(options_df, prices, config)
        elif strategy_type == 'iron_condors':
            candidates = options_analyzer.find_iron_condor_candidates(options_df, prices, config)


        # Convert candidates to dict for JSON response
        candidates_list = candidates.to_dict('records') if not candidates.empty else []

        # Prepare results data
        results_data = {
            'candidates': candidates_list,
            'strategy_type': strategy_type,
            'analysis_timestamp': datetime.now().isoformat()
        }

        # Save analysis results
        options_service.save_analysis_results(account_id, strategy_type, symbols, results_data, market_conditions)

        return jsonify({
            "candidates": candidates_list,
            "market_conditions": market_conditions,
            "config_used": config,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"期权策略分析时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/refresh', methods=['POST'])
def refresh_options_data():
    """刷新期权数据"""
    try:
        data = request.get_json()
        symbols = data.get('symbols', [])
        force_refresh = data.get('force_refresh', False)

        if not symbols:
            return jsonify({"error": "Symbols list cannot be empty"}), 400

        logger.info(f"刷新期权数据: {symbols}, 强制刷新: {force_refresh}")

        # Refresh options data
        options_df, prices = options_data_manager.refresh_options_data(symbols, force_refresh)

        # Get cache status
        cache_status = options_data_manager.get_cached_symbols_status()

        return jsonify({
            "message": f"Successfully refreshed data for {len(symbols)} symbols",
            "options_records": len(options_df),
            "price_records": len(prices),
            "cache_status": cache_status,
            "status": "success"
        })

    except Exception as e:
        logger.error(f"刷新期权数据时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@strategies_bp.route('/options/cache/status', methods=['GET'])
def get_options_cache_status():
    """获取期权数据缓存状态"""
    try:
        cache_status = options_data_manager.get_cached_symbols_status()
        return jsonify({
            "cache_status": cache_status,
            "status": "success"
        })
    except Exception as e:
        logger.error(f"获取缓存状态时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500
