import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Slider,
  InputAdornment,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Restore as RestoreIcon,
  Save as SaveIcon,
  Info as InfoIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { toast } from 'react-hot-toast';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';

const StrategyConfigDialog = ({
  open,
  onClose,
  selectedAccount,
  strategyType,
  currentConfig,
  onConfigSaved
}) => {
  const [config, setConfig] = useState({});
  const [loading, setLoading] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    advanced: false,
    risk: false,
  });

  useEffect(() => {
    if (open && currentConfig) {
      setConfig({ ...currentConfig });
    }
  }, [open, currentConfig]);

  const handleSave = async () => {
    if (!selectedAccount || !strategyType) {
      toast.error('缺少必要参数');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}${API_ENDPOINTS.OPTIONS_CONFIG}/${strategyType}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            account_id: selectedAccount,
            config: config,
            is_default: false,
          }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        toast.success('配置已保存');
        onConfigSaved && onConfigSaved(config);
        onClose();
      } else {
        toast.error(`保存失败: ${data.error}`);
      }
    } catch (error) {
      toast.error(`保存失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    if (currentConfig) {
      setConfig({ ...currentConfig });
      toast.info('配置已重置');
    }
  };

  const updateConfig = (key, value) => {
    setConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNestedConfig = (section, key, value) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const renderBasicSettings = () => {
    const commonFields = [
      {
        key: 'min_dte',
        label: '最小到期天数',
        type: 'number',
        min: 1,
        max: 365,
        help: '期权到期的最少天数，建议20-30天'
      },
      {
        key: 'max_dte',
        label: '最大到期天数',
        type: 'number',
        min: 1,
        max: 365,
        help: '期权到期的最多天数，建议45-60天'
      },
      {
        key: 'min_annual_roi',
        label: '最小年化收益率',
        type: 'percentage',
        min: 0,
        max: 2,
        step: 0.01,
        help: '期权策略的最小年化收益率要求'
      },
      {
        key: 'max_candidates',
        label: '最大候选数量',
        type: 'number',
        min: 1,
        max: 50,
        help: '返回的最大候选期权数量'
      }
    ];

    return (
      <Grid container spacing={3}>
        {commonFields.map((field) => (
          <Grid item xs={12} sm={6} key={field.key}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                {field.label}
              </Typography>
              <Tooltip title={field.help}>
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            {field.type === 'percentage' ? (
              <TextField
                fullWidth
                type="number"
                value={config[field.key] || 0}
                onChange={(e) => updateConfig(field.key, parseFloat(e.target.value))}
                inputProps={{
                  min: field.min,
                  max: field.max,
                  step: field.step || 0.01
                }}
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>
                }}
                size="small"
              />
            ) : (
              <TextField
                fullWidth
                type="number"
                value={config[field.key] || 0}
                onChange={(e) => updateConfig(field.key, parseInt(e.target.value))}
                inputProps={{
                  min: field.min,
                  max: field.max
                }}
                size="small"
              />
            )}
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderStrategySpecificSettings = () => {
    if (strategyType === 'cash_secured_puts') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最大Delta值
              </Typography>
              <Tooltip title="看跌期权的最大Delta值，建议0.20-0.30">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.max_delta || 0.30}
              onChange={(e) => updateConfig('max_delta', parseFloat(e.target.value))}
              inputProps={{ min: 0.05, max: 0.50, step: 0.01 }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最小缓冲比例
              </Typography>
              <Tooltip title="当前价格与行权价之间的最小缓冲比例">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.min_buffer_percent || 0.05}
              onChange={(e) => updateConfig('min_buffer_percent', parseFloat(e.target.value))}
              inputProps={{ min: 0.01, max: 0.20, step: 0.01 }}
              InputProps={{
                endAdornment: <InputAdornment position="end">%</InputAdornment>
              }}
              size="small"
            />
          </Grid>
        </Grid>
      );
    }

    if (strategyType === 'covered_calls') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最大Delta值
              </Typography>
              <Tooltip title="看涨期权的最大Delta值，建议0.20-0.30">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.max_delta || 0.30}
              onChange={(e) => updateConfig('max_delta', parseFloat(e.target.value))}
              inputProps={{ min: 0.05, max: 0.50, step: 0.01 }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最小上涨缓冲
              </Typography>
              <Tooltip title="行权价与当前价格之间的最小上涨空间">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.min_upside_buffer || 0.02}
              onChange={(e) => updateConfig('min_upside_buffer', parseFloat(e.target.value))}
              inputProps={{ min: 0.01, max: 0.20, step: 0.01 }}
              InputProps={{
                endAdornment: <InputAdornment position="end">%</InputAdornment>
              }}
              size="small"
            />
          </Grid>
        </Grid>
      );
    }

    if (strategyType === 'iron_condors') {
      return (
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                目标看跌Delta
              </Typography>
              <Tooltip title="卖出看跌期权的目标Delta值">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.target_delta_short_put || 0.20}
              onChange={(e) => updateConfig('target_delta_short_put', parseFloat(e.target.value))}
              inputProps={{ min: 0.10, max: 0.40, step: 0.01 }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                目标看涨Delta
              </Typography>
              <Tooltip title="卖出看涨期权的目标Delta值">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.target_delta_short_call || 0.20}
              onChange={(e) => updateConfig('target_delta_short_call', parseFloat(e.target.value))}
              inputProps={{ min: 0.10, max: 0.40, step: 0.01 }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最小翼宽
              </Typography>
              <Tooltip title="期权价差的最小宽度">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.min_wing_width || 5}
              onChange={(e) => updateConfig('min_wing_width', parseInt(e.target.value))}
              inputProps={{ min: 1, max: 50 }}
              size="small"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 500 }}>
                最大翼宽
              </Typography>
              <Tooltip title="期权价差的最大宽度">
                <IconButton size="small" sx={{ ml: 0.5 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <TextField
              fullWidth
              type="number"
              value={config.max_wing_width || 20}
              onChange={(e) => updateConfig('max_wing_width', parseInt(e.target.value))}
              inputProps={{ min: 1, max: 100 }}
              size="small"
            />
          </Grid>
        </Grid>
      );
    }

    return null;
  };

  const getStrategyTitle = () => {
    const titles = {
      cash_secured_puts: '现金担保看跌期权配置',
      covered_calls: '备兑看涨期权配置',
      iron_condors: '铁鹰策略配置'
    };
    return titles[strategyType] || '策略配置';
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">{getStrategyTitle()}</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          <Alert severity="info">
            调整这些参数以优化您的期权策略。建议在实际交易前先进行回测验证。
          </Alert>
        </Box>

        <Accordion
          expanded={expandedSections.basic}
          onChange={() => setExpandedSections(prev => ({ ...prev, basic: !prev.basic }))}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">基础设置</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {renderBasicSettings()}
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedSections.advanced}
          onChange={() => setExpandedSections(prev => ({ ...prev, advanced: !prev.advanced }))}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">策略特定设置</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {renderStrategySpecificSettings()}
          </AccordionDetails>
        </Accordion>

        <Accordion
          expanded={expandedSections.risk}
          onChange={() => setExpandedSections(prev => ({ ...prev, risk: !prev.risk }))}
        >
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">排序和筛选</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth size="small">
                  <InputLabel>排序方式</InputLabel>
                  <Select
                    value={config.sort_by || 'winRateScore'}
                    onChange={(e) => updateConfig('sort_by', e.target.value)}
                    label="排序方式"
                  >
                    <MenuItem value="winRateScore">胜率评分</MenuItem>
                    <MenuItem value="annualizedRoi">年化收益率</MenuItem>
                    <MenuItem value="premium">权利金</MenuItem>
                    <MenuItem value="bufferPercent">缓冲比例</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleReset} startIcon={<RestoreIcon />}>
          重置
        </Button>
        <Button onClick={onClose}>
          取消
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          startIcon={<SaveIcon />}
          disabled={loading}
        >
          {loading ? '保存中...' : '保存配置'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default StrategyConfigDialog;
