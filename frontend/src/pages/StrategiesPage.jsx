import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Tooltip,
  IconButton,
  TextField,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  useTheme,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Radio,
  TableSortLabel,
  CardHeader,
  CardActions,
  Tabs,
  Tab,
  Autocomplete,
} from '@mui/material';
import NavBar from '../components/layout/NavBar';
import MetricTooltip from '../components/common/MetricTooltip';
import { API_BASE_URL, API_ENDPOINTS } from '../constants/config';
import {
  RISK_METRIC_DESCRIPTIONS,
  RISK_METRIC_NAMES,
  PERFORMANCE_METRIC_DESCRIPTIONS,
  PERFORMANCE_METRIC_NAMES
} from '../constants/metricDescriptions';
import Chart from '../components/charts/Chart';
import axios from 'axios';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import { alpha } from '@mui/material/styles';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import InsightsIcon from '@mui/icons-material/Insights';
import PieChartIcon from '@mui/icons-material/PieChart';
import TimelineIcon from '@mui/icons-material/Timeline';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import { toast } from 'react-hot-toast';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import GppGoodIcon from '@mui/icons-material/GppGood';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import WarningIcon from '@mui/icons-material/Warning';
import SecurityIcon from '@mui/icons-material/Security';
import FuturePredictionsDialog from '../components/forms/StrategiesPageFuturePredictions'; // Added import
import { GradientTypography } from '../styles/common';
import OptionsAnalysisPanel from '../components/options/OptionsAnalysisPanel';
import WatchlistDialog from '../components/options/WatchlistDialog';

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  whiteSpace: 'nowrap',
  padding: '12px 16px',
  fontWeight: 'bold',
  borderBottom: `1px solid ${theme.palette.divider}`,
  '&.header': {
    backgroundColor: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[800],
    color: theme.palette.text.primary,
    fontWeight: '600',
    borderBottom: `2px solid ${theme.palette.divider}`,
  },
}));

const StyledTable = styled(Table)(({ theme }) => ({
  '& .MuiTableCell-root': {
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  '& .MuiTableRow-root': {
    '&:last-child td, &:last-child th': {
      borderBottom: 0,
    },
  },
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:nth-of-type(odd)': {
    backgroundColor: theme.palette.action.hover,
  },
  '&:hover': {
    backgroundColor: theme.palette.action.selected,
    cursor: 'pointer',
  },
  '& .MuiTableCell-root': {
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
}));

const FilterButton = styled(Button)(({ theme, $active }) => ({
  fontWeight: $active ? 'bold' : 'normal',
  borderColor: $active ? theme.palette.primary.main : theme.palette.divider,
  color: $active ? theme.palette.primary.main : theme.palette.text.secondary,
  backgroundColor: $active ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
  '&:hover': {
    backgroundColor: $active ? alpha(theme.palette.primary.main, 0.2) : theme.palette.action.hover,
  },
}));

const StyledCard = styled(Card)(({ theme }) => ({
  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
  borderRadius: 12,
  position: 'relative',
  overflow: 'visible',
  transition: 'transform 0.2s, box-shadow 0.2s',
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 6px 20px rgba(0,0,0,0.1)',
  },
}));

const TabPanel = (props) => {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Common stock symbols for autocomplete suggestions
const commonSymbols = [
  'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX', 'ADBE', 'CRM',
  'ORCL', 'INTC', 'AMD', 'PYPL', 'UBER', 'LYFT', 'ZOOM', 'DOCU', 'SHOP', 'SQ',
  'ROKU', 'TWLO', 'OKTA', 'SNOW', 'PLTR', 'COIN', 'RBLX', 'HOOD', 'SOFI', 'UPST',
  'SPY', 'QQQ', 'IWM', 'VTI', 'VOO', 'VEA', 'VWO', 'BND', 'AGG', 'TLT'
];

const StrategiesPage = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [accounts, setAccounts] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [availableStocks, setAvailableStocks] = useState({
    position_symbols: [],
    cached_symbols: []
  });
  const [selectedStocks, setSelectedStocks] = useState([]);
  const [strategyData, setStrategyData] = useState({
    positions: [],
    recommendations: [],
    risk_metrics: {},
    performance_metrics: {}
  });
  const [chartData, setChartData] = useState({});
  const [openTechnicalAnalysis, setOpenTechnicalAnalysis] = useState(false);
  const [selectedSymbolForAnalysis, setSelectedSymbolForAnalysis] = useState('');

  const [symbolInput, setSymbolInput] = useState('');
  const [detailedRecommendation, setDetailedRecommendation] = useState(null);
  const [showPositionAnalysis, setShowPositionAnalysis] = useState(false);

  // --- NEW: Separate Loading States ---
  const [isAccountMetricsLoading, setIsAccountMetricsLoading] = useState(false);
  const [isPositionAnalysisLoading, setIsPositionAnalysisLoading] = useState(false);
  // --- END: Separate Loading States ---

  // State for table sorting
  const [sortConfig, setSortConfig] = useState({ key: 'symbol', direction: 'asc' });

  // State for AI Detailed Analysis
  const [detailedAiAnalysis, setDetailedAiAnalysis] = useState({
    symbol: null,
    loading: false,
    data: null,
    error: null
  });
  const [isDetailedAiDialogOpen, setIsDetailedAiDialogOpen] = useState(false);

  // --- NEW: Y轴缩放模式状态 ---
  const [yAxisAutoScale, setYAxisAutoScale] = useState(true);
  // --- END: Y轴缩放模式状态 ---

  // --- NEW: Future Predictions State ---
  const [openFuturePredictionsDialog, setOpenFuturePredictionsDialog] = useState(false);
  const [selectedSymbolForPredictions, setSelectedSymbolForPredictions] = useState('');
  const [futurePredictionsData, setFuturePredictionsData] = useState(null);
  const [isFetchingPredictions, setIsFetchingPredictions] = useState(false);
  const [predictionsError, setPredictionsError] = useState(null);
  const futurePredictionsAbortControllerRef = useRef(null); // Ref for AbortController
  // --- END: Future Predictions State ---

  // --- Options Analysis State ---
  const [analysisMode, setAnalysisMode] = useState('traditional'); // 'traditional' or 'options'
  const [watchlistDialogOpen, setWatchlistDialogOpen] = useState(false);
  // --- END: Options Analysis State ---

  // --- Memoized value for the Technical Analysis Chart --- 
  useEffect(() => {
    fetchAccounts();

    // Cleanup for future predictions fetch
    return () => {
      if (futurePredictionsAbortControllerRef.current) {
        futurePredictionsAbortControllerRef.current.abort();
      }
    };
  }, []);

  // Fetch available stocks AND initial account metrics when account changes
  useEffect(() => {
    const fetchInitialData = async () => {
      if (!selectedAccount) return;

      setLoading(true); // Use main loading indicator for initial data fetch
      setError(null);
      // Reset selections and analysis data when account changes
      setSelectedStocks([]);
      setStrategyData({ positions: [], recommendations: [], risk_metrics: {}, performance_metrics: {} });
      setShowPositionAnalysis(false);

      try {
        // Fetch available stocks first
        const stocksResponse = await fetch(`${API_BASE_URL}/api/strategies/stocks?account_id=${selectedAccount}`);
        if (!stocksResponse.ok) {
          throw new Error('Failed to fetch available stocks');
        }
        const stocksData = await stocksResponse.json();
        setAvailableStocks(stocksData);
        // Set default selection to position stocks (can be empty)
        setSelectedStocks(stocksData.position_symbols || []);

        // --- Fetch initial account metrics --- 
        await fetchAccountMetrics(selectedAccount);

      } catch (error) {
        setError(error.message);
        toast.error(`获取账户初始数据失败: ${error.message}`);
        // Reset metrics on error too
        setStrategyData(prevData => ({ ...prevData, risk_metrics: {}, performance_metrics: {} }));
      } finally {
        setLoading(false); // Stop main loading indicator
      }
    };

    fetchInitialData();
  }, [selectedAccount]); // Rerun when selectedAccount changes

  // --- Moved Sorting Logic Here ---
  const positionsToRender = useMemo(() =>
    Array.isArray(strategyData.positions) ? strategyData.positions : [],
    [strategyData.positions]);

  const sortedPositions = useMemo(() => {
    let sortableItems = [...positionsToRender];
    if (sortConfig.key) {
      sortableItems.sort((a, b) => {
        let aValue = a[sortConfig.key];
        let bValue = b[sortConfig.key];

        const typeA = typeof aValue;
        const typeB = typeof bValue;

        if (aValue == null && bValue == null) return 0;
        if (aValue == null) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (bValue == null) return sortConfig.direction === 'ascending' ? 1 : -1;

        if (typeA === 'number' && typeB === 'number') {
          if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
          if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
          return 0;
        }

        aValue = String(aValue).toUpperCase();
        bValue = String(bValue).toUpperCase();
        if (aValue < bValue) return sortConfig.direction === 'ascending' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'ascending' ? 1 : -1;
        return 0;
      });
    }
    return sortableItems;
  }, [positionsToRender, sortConfig]);
  // --- End Moved Sorting Logic ---

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}${API_ENDPOINTS.ACCOUNTS}`);
      if (!response.ok) {
        throw new Error('Failed to fetch accounts');
      }
      const data = await response.json();

      // Sort accounts by current_value in descending order
      const sortedAccounts = [...data].sort((a, b) => b.current_value - a.current_value);

      setAccounts(sortedAccounts);

      if (sortedAccounts.length > 0) {
        // Select the account with the highest value
        setSelectedAccount(sortedAccounts[0].id);
      }
    } catch (error) {
      setError(error.message);
      toast.error(`获取账户失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const fetchAccountMetrics = async (accountId) => {
    console.log(`[fetchAccountMetrics] Fetching initial metrics for account: ${accountId}`);
    // setIsAnalyzing(true); // Use the analyzing state for metric loading indication
    setIsAccountMetricsLoading(true); // Use specific state
    setError(null);
    try {
      // Call analysis endpoint with account_id but NO symbols
      const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.STRATEGY_ANALYSIS}`, {
        params: {
          account_id: accountId,
          // symbols: [] // Explicitly empty or omit if backend handles it
        }
      });

      const responseData = response.data;
      // -- Modified Validation --
      // Check if responseData itself is an object and if metrics exist
      if (!responseData || typeof responseData !== 'object') {
        console.warn('[fetchAccountMetrics] Invalid response format received:', responseData);
        // Set empty metrics to avoid breaking UI, but log error
        setStrategyData(prevData => ({ ...prevData, risk_metrics: {}, performance_metrics: {} }));
        toast.error('获取账户指标时响应格式无效。');
      } else {
        // We have an object, update metrics safely, defaulting to {} if missing
        setStrategyData(prevData => ({
          ...prevData, // Keep existing positions/recommendations if any
          risk_metrics: responseData.risk_metrics && typeof responseData.risk_metrics === 'object' ? responseData.risk_metrics : {},
          performance_metrics: responseData.performance_metrics && typeof responseData.performance_metrics === 'object' ? responseData.performance_metrics : {}
        }));
        console.log('[fetchAccountMetrics] Successfully updated account metrics.');
      }
      // -- End Modified Validation --

    } catch (err) {
      console.error('[fetchAccountMetrics] Error fetching account metrics:', err);
      let errorMessage = '获取账户指标失败';
      if (axios.isAxiosError(err)) {
        errorMessage = err.response?.data?.error || err.response?.data?.message || err.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage); // Set global error
      toast.error(errorMessage);
      // Clear metrics on error
      setStrategyData(prevData => ({ ...prevData, risk_metrics: {}, performance_metrics: {} }));
    } finally {
      // setIsAnalyzing(false); // Stop metric loading indicator
      setIsAccountMetricsLoading(false); // Use specific state
    }
  };

  const handleAccountChange = (event) => {
    setSelectedAccount(event.target.value);
    // useEffect will trigger data fetching
  };

  const handleOpenTechnicalAnalysis = async (symbol) => {
    console.log(`[handleOpenTechnicalAnalysis] Opening chart for: ${symbol}`);
    setSelectedSymbolForAnalysis(symbol);
    setOpenTechnicalAnalysis(true);
    try {
      const response = await axios.get(`${API_BASE_URL}/api/strategies/technical-analysis`, {
        params: { symbol }
      });

      let data = response.data;
      // Handle case where backend returns JSON as string instead of parsed object
      if (typeof data === 'string') {
        try {
          data = JSON.parse(data);
        } catch (parseError) {
          console.error(`[handleOpenTechnicalAnalysis] Failed to parse JSON response:`, parseError);
          setError('服务器返回的数据格式无效，请检查后端日志');
          return;
        }
      }


      // Check if we have valid data structure including buy/sell points
      if (!data || typeof data !== 'object' || !data.historicalData || !data.indicators || !Array.isArray(data.buy_points) || !Array.isArray(data.sell_points)) {
        setError('技术分析数据格式无效: 缺少必要数据 (历史, 指标, 买卖点)');
        // Clear chart data for this symbol on error to avoid showing stale data
        setChartData(prevData => ({
          ...prevData,
          [symbol]: {
            series: [],
            options: {},
            buyPoints: [],
            sellPoints: [],
            goldenCrossPoints: [],
            deathCrossPoints: [],
            secondGoldenCrossPoints: []
          }
        }));
        return;
      }

      // Ensure historical data is usable
      if (!data.historicalData.length) {
        console.warn(`[handleOpenTechnicalAnalysis] No historical data for ${symbol}`);
        // Still provide empty structure for chart component
        setChartData(prevData => ({
          ...prevData,
          [symbol]: {
            series: [],
            options: {},
            buyPoints: [], // Ensure keys exist even if empty
            sellPoints: [],
            goldenCrossPoints: [],
            deathCrossPoints: [],
            secondGoldenCrossPoints: [] // 添加二次金叉空数组
          }
        }));
        return; // Exit if no historical data to process
      }

      // Validate historical data content
      const validData = data.historicalData.every(d =>
        d.hasOwnProperty('date') &&
        d.hasOwnProperty('close') &&
        d.hasOwnProperty('high') &&
        d.hasOwnProperty('low') &&
        d.hasOwnProperty('volume') &&
        !isNaN(d.close) &&
        !isNaN(d.high) &&
        !isNaN(d.low) &&
        !isNaN(d.volume)
      );

      if (!validData) {
        console.error('Data contains invalid entries');
        setError('数据格式不完整或包含无效值');
        // Clear chart data for this symbol on error to avoid showing stale data
        setChartData(prevData => ({
          ...prevData,
          [symbol]: {
            series: [],
            options: {},
            buyPoints: [],
            sellPoints: [],
            goldenCrossPoints: [],
            deathCrossPoints: [],
            secondGoldenCrossPoints: []
          }
        }));
        return;
      }

      // Format data for the chart using timestamps for the x-axis
      const formattedData = {
        dates: data.historicalData.map(d => d.date), // Keep dates for potential display, but use timestamps for chart data
        // Map historical data to [timestamp, value]
        close: data.historicalData.map(d => [new Date(d.date).getTime(), d.close]),
        ma20: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.ma20?.[i] ?? null]),
        ma50: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.ma50?.[i] ?? null]),
        rsi: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.rsi?.[i] ?? null]),
        macdLine: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.macd?.macd_line?.[i] ?? null]),
        signalLine: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.macd?.signal_line?.[i] ?? null]),
        smoothPriceOscillator: data.historicalData.map((d, i) => [new Date(d.date).getTime(), data.indicators?.ss_oscillator?.[i] ?? null]),
        buyPoints: data.buy_points || [],
        sellPoints: data.sell_points || [],
        goldenCrossPoints: data.golden_cross_points || [],
        deathCrossPoints: data.death_cross_points || [],
        secondGoldenCrossPoints: data.second_golden_cross_points || [], // 添加二次金叉点
      };

      // Prepare series array for the Chart component
      const chartSeries = [
        {
          name: '股价',
          data: formattedData.close,
          type: 'line',
          // --- Style Enhancement ---
          lineStyle: {
            width: 4,
            shadowColor: alpha(theme.palette.primary.main, 0.3),
            shadowBlur: 8,
            shadowOffsetY: 2
          },
          itemStyle: {
            color: theme.palette.primary.main
          },
          emphasis: {
            focus: 'series',
            lineStyle: {
              width: 4.5,
            }
          },
          z: 10 // 确保股价线在最前面
          // --- End Enhancement ---
        },
        {
          name: '20日均线',
          data: formattedData.ma20,
          type: 'line',
          // --- Style Enhancement ---
          lineStyle: {
            width: 2,
            opacity: 0.7,
            type: 'solid'
          },
          itemStyle: {
            color: theme.palette.secondary.main
          }
          // --- End Enhancement ---
        },
        {
          name: '50日均线',
          data: formattedData.ma50,
          type: 'line',
          // --- Style Enhancement ---
          lineStyle: {
            width: 2,
            opacity: 0.6,
            type: 'dash'
          },
          itemStyle: {
            color: theme.palette.info.main
          }
          // --- End Enhancement ---
        },
        {
          name: 'RSI指标',
          data: formattedData.rsi,
          type: 'line',
          yAxisIndex: 1, // Ensure RSI uses the right Y-axis
          lineStyle: {
            width: 1.5,
            opacity: 0.8
          }
        },
        // --- 简化SS显示 ---
        {
          name: '平滑价格振荡器',
          data: formattedData.smoothPriceOscillator,
          type: 'bar',
          yAxisIndex: 1,
          itemStyle: {
            color: (params) => (params.value[1] >= 0 ? alpha(theme.palette.success.main, 0.6) : alpha(theme.palette.error.main, 0.6))
          },
          barWidth: '60%'
        }
      ].filter(series => series.data.some(d => d && d.length > 1 && d[1] !== null && !isNaN(d[1]))); // Filter series with valid data

      // --- Combine into a single object for the state ---
      const finalChartConfig = {
        series: chartSeries,
        options: { // Pass options directly if needed by the chart component
          tooltip: {
            trigger: 'axis',
            backgroundColor: alpha(theme.palette.background.paper, 0.95),
            borderColor: theme.palette.divider,
            textStyle: { color: theme.palette.text.primary },
            formatter: (params) => {
              if (!params || params.length === 0) return '';
              const date = new Date(params[0].value[0]);
              const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
              let result = `<div style="font-size: 13px; line-height: 1.8;"><strong>${dateStr}</strong><br/>`;

              params.forEach(param => {
                if (param.value && param.value[1] !== null && param.value[1] !== undefined) {
                  const color = param.color || '#666';
                  const markerHtml = `<span style="display:inline-block;margin-right:8px;border-radius:50%;width:8px;height:8px;background-color:${color};"></span>`;
                  const value = param.seriesName.includes('RSI') ?
                    param.value[1].toFixed(1) :
                    `$${param.value[1].toFixed(2)}`;
                  result += `${markerHtml}${param.seriesName}: <strong>${value}</strong><br/>`;
                }
              });

              result += `</div>`;
              return result;
            }
          },
          legend: {
            data: chartSeries.map(s => s.name),
            textStyle: {
              color: theme.palette.text.primary,
              fontSize: 12
            },
            itemGap: 20,
            bottom: 15,
            // 添加图例说明
            tooltip: {
              show: true,
              formatter: (params) => {
                const explanations = {
                  '股价': '股票收盘价走势，主要关注趋势',
                  '20日均线': '20日移动平均线，短期趋势参考',
                  '50日均线': '50日移动平均线，中期趋势参考',
                  'RSI指标': '相对强弱指数，70以上超买，30以下超卖',
                  'Smooth Price Oscillator': '平滑价格振荡器，正值看涨，负值看跌'
                };
                return explanations[params.name] || params.name;
              }
            }
          },
          // --- 添加Y轴配置 ---
          yAxisAutoScale: yAxisAutoScale,
          // Add other ECharts options here like axes formatting based on dates if needed
        },
        buyPoints: formattedData.buyPoints, // Pass points separately (still needed by renderTechnicalAnalysisDialog)
        sellPoints: formattedData.sellPoints,
        goldenCrossPoints: formattedData.goldenCrossPoints,
        deathCrossPoints: formattedData.deathCrossPoints,
        secondGoldenCrossPoints: formattedData.secondGoldenCrossPoints, // 添加二次金叉点
        // dates: formattedData.dates // Dates are no longer the primary x-axis source for Chart component
      };

      // Update chart data state
      setChartData(prevData => ({
        ...prevData,
        [symbol]: finalChartConfig // Store the combined config object
      }));
    } catch (error) {
      console.error('[handleOpenTechnicalAnalysis] Error fetching technical analysis data:', error);
      const errorMsg = error.response?.data?.error || error.message || '获取技术分析数据失败';
      setError(errorMsg); // Set global error state for now
      // Clear chart data for this symbol on error to avoid showing stale data
      setChartData(prevData => ({
        ...prevData,
        [symbol]: null // Explicitly set to null on error
      }));
    }
  };

  const handleCloseTechnicalAnalysis = () => {
    setOpenTechnicalAnalysis(false);
  };



  // Handle adding a stock symbol manually
  const handleAddSymbol = () => {
    const symbol = symbolInput.trim().toUpperCase();
    if (symbol && !selectedStocks.includes(symbol)) {
      setSelectedStocks([...selectedStocks, symbol]);
      setSymbolInput('');
    }
  };

  // Handle removing a stock symbol
  const handleRemoveSymbol = (symbolToRemove) => {
    setSelectedStocks(selectedStocks.filter(symbol => symbol !== symbolToRemove));
  };

  const startAnalysis = async () => {
    if (!selectedAccount || selectedStocks.length === 0) {
      toast.error('请先选择账户和至少一个股票以进行分析。');
      return;
    }
    console.log(`[startAnalysis] Analyzing ${selectedStocks.length} selected stocks for account ${selectedAccount}`);
    // setIsAnalyzing(true); // Show loading indicator for specific stock analysis
    setIsPositionAnalysisLoading(true); // Use specific state
    setError(null);

    try {
      console.log('[startAnalysis] Making API call...'); // Log before API call
      const response = await axios.get(`${API_BASE_URL}${API_ENDPOINTS.STRATEGY_ANALYSIS}`, {
        params: {
          symbols: selectedStocks, // Send selected stocks
          account_id: selectedAccount
        }
      });

      const responseData = response.data;
      if (!responseData || typeof responseData !== 'object') {
        throw new Error('无效的API响应格式');
      }

      // Check positions data specifically, as it's crucial for the dialog
      const positionsData = responseData.positions;
      if (!Array.isArray(positionsData)) {
        console.error('Invalid positions data received:', positionsData);
        throw new Error('收到的持仓数据格式无效');
      }

      // --- MODIFIED STATE UPDATE ---
      // Only update positions and recommendations, keep existing metrics
      setStrategyData(prevData => ({
        ...prevData, // Keep existing data (risk_metrics, performance_metrics, etc.)
        positions: Array.isArray(responseData.positions) ? responseData.positions : [], // Update positions safely
        recommendations: Array.isArray(responseData.recommendations) ? responseData.recommendations : [], // Update recommendations safely
      }));
      // --- END MODIFIED STATE UPDATE ---
      setShowPositionAnalysis(true); // OPEN the position analysis dialog
      toast.success('选定股票分析完成!');
    } catch (err) {
      console.error('[startAnalysis] Error analyzing selected stocks:', err); // Log error details
      let errorMessage = '选定股票分析失败';
      if (axios.isAxiosError(err)) {
        if (err.response) {
          errorMessage = `服务器错误 (${err.response.status}): ${err.response.data?.error || err.response.data?.message || err.response.statusText}`;
        } else if (err.request) {
          errorMessage = '无法连接到服务器，请检查网络连接';
        } else {
          errorMessage = err.message;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      toast.error(errorMessage);
      // Optionally clear or revert metrics? For now, just show error.
      // setStrategyData(prevData => ({ ...prevData, risk_metrics: {}, performance_metrics: {} }));
    } finally {
      setIsPositionAnalysisLoading(false); // Use specific state
    }
  };

  const renderMetricsCard = (title, icon, metricsData, metricNames, metricDescriptions) => {
    const hasData = metricsData && Object.keys(metricsData).length > 0;
    // Get all possible metric keys from the names/descriptions provided
    const allMetricKeys = Object.keys(metricNames);

    return (
      <StyledCard>
        <CardContent sx={{ flexGrow: 1, p: 2.5 }}>
          {/* Card Header */}
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            mb: 2,
            pb: 1.5,
            borderBottom: theme => `1px solid ${theme.palette.divider}`
          }}>
            {React.cloneElement(icon, { fontSize: 'medium' })}
            <Typography variant="h6" sx={{ ml: 1.5, fontWeight: 600 }}>{title}</Typography>
          </Box>

          {/* Card Body */}
          {isAccountMetricsLoading ? ( // Check specific state for these cards
            // Show loading state when analysis is running
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120, py: 2 }}>
              <CircularProgress size={28} />
              <Typography sx={{ ml: 2 }} variant="body2">计算中...</Typography>
            </Box>
          ) : (
            // Always show the grid structure, using placeholders if no data
            <Grid container spacing={2.5} sx={{ mt: 0.5 }}>
              {allMetricKeys.map((key) => {
                const value = hasData ? metricsData[key] : undefined; // Get value if data exists
                const displayValue = value !== undefined && value !== null
                  ? (typeof value === 'number' ? value.toFixed(3) : String(value))
                  : "---"; // Default placeholder

                // --- Coloring Logic (copied from previous version) ---
                const isRiskMetric = title.includes('风险');
                const isPositiveValue = typeof value === 'number' && value > 0;
                const isNegativeValue = typeof value === 'number' && value < 0;
                const valueColor = () => {
                  // Only apply color if we have actual data, otherwise default
                  if (value === undefined || value === null) return 'text.secondary';
                  if (isRiskMetric) {
                    return isPositiveValue && value > 0.5 ? 'error.main' :
                      isNegativeValue ? 'success.main' : 'text.primary';
                  } else {
                    return isPositiveValue ? 'success.main' :
                      isNegativeValue ? 'error.main' : 'text.primary';
                  }
                };
                // --- End Coloring Logic ---

                return (
                  <Grid item xs={12} sm={6} md={4} key={key}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        bgcolor: theme => alpha(theme.palette.background.default, 0.5),
                        borderRadius: 1,
                        height: '100%'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {metricNames[key] || key}
                          </Typography>
                          <MetricTooltip description={metricDescriptions[key]} />
                        </Box>
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          color: valueColor(), // Apply color logic or default
                          mt: 0.5,
                          opacity: value === undefined || value === null ? 0.6 : 1, // Dim placeholder
                        }}
                      >
                        {displayValue}
                      </Typography>
                    </Paper>
                  </Grid>
                );
              })}
            </Grid>
          )}
        </CardContent>
      </StyledCard>
    );
  };

  // 持仓分析对话框 (Uses sortedPositions calculated above)
  const renderPositionAnalysisDialog = () => {
    console.log(`[renderPositionAnalysisDialog] Rendering. showPositionAnalysis state: ${showPositionAnalysis}`); // Log render and state
    return (
      <Dialog
        open={showPositionAnalysis}
        onClose={() => {
          console.log('[renderPositionAnalysisDialog] onClose triggered.'); // Log onClose trigger
          setShowPositionAnalysis(false);
        }}
        maxWidth="xl"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TimelineIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="h6">持仓分析</Typography>
            </Box>
            <Chip
              label={`${sortedPositions.length} 只股票`}
              color="primary"
              variant="outlined"
            />
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <TableContainer component={Paper} variant="outlined">
            <StyledTable size="small">
              <TableHead>
                <TableRow>
                  <StyledTableCell className="header">
                    <TableSortLabel
                      active={sortConfig.key === 'symbol'}
                      direction={sortConfig.key === 'symbol' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('symbol')}
                    >
                      股票代码
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'quantity'}
                      direction={sortConfig.key === 'quantity' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('quantity')}
                    >
                      持仓数量
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'current_price'}
                      direction={sortConfig.key === 'current_price' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('current_price')}
                    >
                      当前价格
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'avg_price'}
                      direction={sortConfig.key === 'avg_price' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('avg_price')}
                    >
                      持仓成本价
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'current_value'}
                      direction={sortConfig.key === 'current_value' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('current_value')}
                    >
                      持仓市值
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'unrealized_pnl'}
                      direction={sortConfig.key === 'unrealized_pnl' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('unrealized_pnl')}
                    >
                      未实现盈亏 ($)
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="right">
                    <TableSortLabel
                      active={sortConfig.key === 'unrealized_pnl_pct'}
                      direction={sortConfig.key === 'unrealized_pnl_pct' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('unrealized_pnl_pct')}
                    >
                      盈亏比例 (%)
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="center">
                    <TableSortLabel
                      active={sortConfig.key === 'rsi'}
                      direction={sortConfig.key === 'rsi' ? sortConfig.direction : 'asc'}
                      onClick={() => handleRequestSort('rsi')}
                    >
                      RSI
                    </TableSortLabel>
                  </StyledTableCell>
                  <StyledTableCell className="header" align="center">技术分析</StyledTableCell>
                  <StyledTableCell className="header" align="center">模型预测</StyledTableCell>
                  <StyledTableCell className="header" align="center">AI详细分析</StyledTableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {sortedPositions.map((position) => (
                  <StyledTableRow key={position.symbol}>
                    <StyledTableCell>{position.symbol}</StyledTableCell>
                    <StyledTableCell align="right">{(position.quantity || 0).toFixed(2)}</StyledTableCell>
                    <StyledTableCell align="right">${(position.current_price || 0).toFixed(2)}</StyledTableCell>
                    <StyledTableCell align="right">${(position.avg_price || 0).toFixed(2)}</StyledTableCell>
                    <StyledTableCell align="right">${(position.current_value || 0).toFixed(2)}</StyledTableCell>
                    <StyledTableCell
                      align="right"
                      sx={{ color: (position.unrealized_pnl || 0) >= 0 ? 'success.main' : 'error.main' }}
                    >
                      ${(position.unrealized_pnl || 0).toFixed(2)}
                    </StyledTableCell>
                    <StyledTableCell
                      align="right"
                      sx={{ color: (position.unrealized_pnl_pct || 0) >= 0 ? 'success.main' : 'error.main' }}
                    >
                      {((position.unrealized_pnl_pct || 0) * 100).toFixed(2)}%
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      <Typography
                        variant="body2"
                        color={
                          (position.rsi || 0) > 70 ? 'error.main' :
                            (position.rsi || 0) < 30 ? 'success.main' :
                              'text.primary'
                        }
                      >
                        {position.rsi != null ? (position.rsi || 0).toFixed(1) : 'N/A'}
                      </Typography>
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      <Tooltip title={`查看 ${position.symbol} 的技术图表`}>
                        <IconButton
                          size="small"
                          onClick={() => handleOpenTechnicalAnalysis(position.symbol)}
                        >
                          <TimelineIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      <Tooltip title={`查看 ${position.symbol} 的模型预测曲线`}>
                        <IconButton
                          size="small"
                          onClick={() => handleOpenFuturePredictions(position.symbol)}
                          color="secondary" // Or another appropriate color
                          disabled={isFetchingPredictions && selectedSymbolForPredictions === position.symbol}
                        >
                          {isFetchingPredictions && selectedSymbolForPredictions === position.symbol ?
                            <CircularProgress size={16} color="inherit" /> :
                            <InsightsIcon fontSize="small" /> /* Using InsightsIcon as an example */}
                        </IconButton>
                      </Tooltip>
                    </StyledTableCell>
                    <StyledTableCell align="center">
                      <Tooltip title={`获取 ${position.symbol} 的详细AI分析`}>
                        <IconButton
                          size="small"
                          onClick={() => handleFetchDetailedAiAnalysis(position.symbol)}
                          color="primary"
                          disabled={detailedAiAnalysis.loading && detailedAiAnalysis.symbol === position.symbol}
                        >
                          {detailedAiAnalysis.loading && detailedAiAnalysis.symbol === position.symbol ?
                            <CircularProgress size={16} color="inherit" /> :
                            <AutoAwesomeIcon fontSize="small" />}
                        </IconButton>
                      </Tooltip>
                    </StyledTableCell>
                  </StyledTableRow>
                ))}
                {sortedPositions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={11} align="center">
                      <Typography variant="body1" color="text.secondary" sx={{ py: 3 }}>
                        {isPositionAnalysisLoading ? '正在加载分析数据...' : '没有持仓数据可显示，请先选择股票并点击分析按钮。'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </StyledTable>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowPositionAnalysis(false)}>关闭</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // 渲染AI详细分析对话框
  const renderDetailedAiDialog = () => {
    const handleClose = () => {
      setIsDetailedAiDialogOpen(false);
    };

    return (
      <Dialog
        open={isDetailedAiDialogOpen}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        aria-labelledby="detailed-ai-dialog-title"
      >
        <DialogTitle id="detailed-ai-dialog-title">
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <AutoAwesomeIcon sx={{ mr: 1, color: 'primary.main' }} />
              {`详细AI分析: ${detailedAiAnalysis.symbol || ''}`}
            </Box>
            <IconButton onClick={handleClose} size="small"><CloseIcon /></IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {detailedAiAnalysis.loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
              <CircularProgress />
              <Typography sx={{ ml: 2 }}>正在加载AI分析...</Typography>
            </Box>
          )}
          {detailedAiAnalysis.error && (
            <Alert severity="error" sx={{ my: 2 }}>错误: {detailedAiAnalysis.error}</Alert>
          )}
          {detailedAiAnalysis.data && (
            <Stack spacing={3}>
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>分析概述</Typography>
                <Paper elevation={0} sx={{ p: 2, bgcolor: theme.palette.mode === 'light' ? 'grey.50' : 'grey.900', borderRadius: 2 }}>
                  <Typography variant="body1" paragraph sx={{ mb: 0 }}>
                    {detailedAiAnalysis.data.summary || '无概述信息。'}
                  </Typography>
                </Paper>
              </Box>
              <Divider />
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>建议理由</Typography>
                <List dense>
                  {(detailedAiAnalysis.data.reasons || ['无理由信息。']).map((reason, index) => (
                    <ListItem key={index} disablePadding>
                      <ListItemIcon sx={{ minWidth: 32 }}><CheckCircleOutlineIcon fontSize="small" color="success" /></ListItemIcon>
                      <ListItemText primary={reason} primaryTypographyProps={{ variant: 'body2' }} />
                    </ListItem>
                  ))}
                </List>
              </Box>
              <Divider />
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>行动计划</Typography>
                <Box>
                  {(detailedAiAnalysis.data.action_plan || []).length > 0 ?
                    (detailedAiAnalysis.data.action_plan || []).map((plan, index) => (
                      <Accordion key={index} defaultExpanded={index === 0} variant="outlined" sx={{ mb: 1 }}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography sx={{ fontWeight: 'medium' }}>{plan.title || `步骤 ${index + 1}`}</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Typography variant="body2">{plan.description || '无详细说明。'}</Typography>
                        </AccordionDetails>
                      </Accordion>
                    ))
                    : (
                      <Typography variant="body2" color="text.secondary">无行动计划信息。</Typography>
                    )}
                </Box>
              </Box>
              <Divider />
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>详细分析</Typography>
                <Paper elevation={0} sx={{ p: 2, bgcolor: theme.palette.mode === 'light' ? 'grey.50' : 'grey.900', borderRadius: 2 }}>
                  <Box mb={2}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>基本面分析</Typography>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                      {detailedAiAnalysis.data.analysis?.fundamental || '无基本面分析信息。'}
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 2 }} />
                  <Box mb={2}>
                    <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'medium' }}>技术面分析</Typography>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                      {detailedAiAnalysis.data.analysis?.technical || '无技术面分析信息。'}
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 2 }} />
                </Paper>
              </Box>
              <Divider />
              <Box>
                <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold', mt: 2 }}>主要风险</Typography>
                <List dense>
                  {(detailedAiAnalysis.data.risks || ['无风险信息。']).map((risk, index) => (
                    <ListItem key={index} disablePadding>
                      <ListItemIcon sx={{ minWidth: 32 }}><WarningIcon fontSize="small" color="warning" /></ListItemIcon>
                      <ListItemText primary={risk} primaryTypographyProps={{ variant: 'body2' }} />
                    </ListItem>
                  ))}
                </List>
              </Box>
            </Stack>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>关闭</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Sorting handler (Re-add this function)
  const handleRequestSort = (property) => {
    const isAsc = sortConfig.key === property && sortConfig.direction === 'ascending';
    setSortConfig({ key: property, direction: isAsc ? 'descending' : 'ascending' });
  };

  // 渲染技术分析对话框
  const renderTechnicalAnalysisDialog = () => {
    const chartConfig = chartData[selectedSymbolForAnalysis];

    let dialogContent;

    if (chartConfig && chartConfig.series) {
      // Prepare series with markPoints for buy/sell signals
      const seriesWithMarkPoints = chartConfig.series.map(s => {
        if (s.name === '股价') { // Add buy/sell markers to the closing price series
          const markPoints = [];
          if (chartConfig.buyPoints && chartConfig.buyPoints.length > 0) {
            markPoints.push(...chartConfig.buyPoints.map(p => ({
              coord: p, // p should be [timestamp, price]
              symbol: 'triangle', // 使用三角形表示买入
              symbolSize: 12,
              itemStyle: {
                color: theme.palette.success.main,
                borderColor: theme.palette.background.paper,
                borderWidth: 2
              },
              label: { show: false } // 简化标签显示
            })));
          }
          if (chartConfig.sellPoints && chartConfig.sellPoints.length > 0) {
            markPoints.push(...chartConfig.sellPoints.map(p => ({
              coord: p, // p should be [timestamp, price]
              symbol: 'diamond', // 使用菱形表示卖出
              symbolSize: 12,
              itemStyle: {
                color: theme.palette.error.main,
                borderColor: theme.palette.background.paper,
                borderWidth: 2
              },
              label: { show: false } // 简化标签显示
            })));
          }
          // --- 简化金叉死叉显示 ---
          if (chartConfig.goldenCrossPoints && chartConfig.goldenCrossPoints.length > 0) {
            markPoints.push(...chartConfig.goldenCrossPoints.map(p => ({
              coord: p,
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                color: theme.palette.warning.main,
                borderColor: theme.palette.background.paper,
                borderWidth: 1,
              },
              label: {
                show: true,
                formatter: '金',
                position: 'top',
                color: theme.palette.text.primary,
                distance: 8,
                fontSize: 10,
                fontWeight: 'bold',
                backgroundColor: alpha(theme.palette.warning.main, 0.1),
                padding: [2, 4],
                borderRadius: 3
              }
            })));
          }
          // --- 添加二次金叉显示 ---
          if (chartConfig.secondGoldenCrossPoints && chartConfig.secondGoldenCrossPoints.length > 0) {
            markPoints.push(...chartConfig.secondGoldenCrossPoints.map(p => ({
              coord: p,
              symbol: 'circle',
              symbolSize: 12, // 稍大一些以突出显示
              itemStyle: {
                color: theme.palette.success.main, // 使用不同颜色表示二次金叉
                borderColor: theme.palette.background.paper,
                borderWidth: 2, // 更粗的边框
              },
              label: {
                show: true,
                formatter: '二金', // 显示"二金"
                position: 'top',
                color: theme.palette.text.primary,
                distance: 10,
                fontSize: 11, // 稍大的字体
                fontWeight: 'bold',
                backgroundColor: alpha(theme.palette.success.main, 0.15),
                padding: [3, 5], // 更大的内边距
                borderRadius: 4
              }
            })));
          }
          if (chartConfig.deathCrossPoints && chartConfig.deathCrossPoints.length > 0) {
            markPoints.push(...chartConfig.deathCrossPoints.map(p => ({
              coord: p,
              symbol: 'circle',
              symbolSize: 10,
              itemStyle: {
                color: theme.palette.info.main,
                borderColor: theme.palette.background.paper,
                borderWidth: 1,
              },
              label: {
                show: true,
                formatter: '死',
                position: 'bottom',
                color: theme.palette.text.primary,
                distance: 8,
                fontSize: 10,
                fontWeight: 'bold',
                backgroundColor: alpha(theme.palette.info.main, 0.1),
                padding: [2, 4],
                borderRadius: 3
              }
            })));
          }
          return { ...s, markPoint: { data: markPoints } };
        }
        return s;
      });

      if (seriesWithMarkPoints.length > 0) {
        dialogContent = (
          <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <Chart
              symbol={selectedSymbolForAnalysis}
              series={seriesWithMarkPoints} // Pass the modified series
              options={chartConfig.options}
              height={400}
            />
          </Box>
        );
      }
    }

    if (!dialogContent && error && selectedSymbolForAnalysis) {
      dialogContent = <Alert severity="error" sx={{ m: 2 }}>{error}</Alert>;
    } else if (!dialogContent) {
      dialogContent = (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexGrow: 1 }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>正在加载图表数据...</Typography>
        </Box>
      );
    }

    return (
      <Dialog
        open={openTechnicalAnalysis}
        onClose={handleCloseTechnicalAnalysis}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <TimelineIcon sx={{ mr: 1, color: 'secondary.main' }} />
              <Box>
                <Typography variant="h6">
                  技术分析图表: {selectedSymbolForAnalysis}
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                  股价走势 + 均线 + RSI + 平滑价格振荡器 | 金叉看涨 · 二金更强 · 死叉看跌 | 点击图例可隐藏/显示指标
                </Typography>
              </Box>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton onClick={handleCloseTechnicalAnalysis} size="small"><CloseIcon /></IconButton>
            </Box>
          </Box>
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            minHeight: 400,
            display: 'flex',
            flexDirection: 'column',
            // Add a subtle gradient background
            background: `linear-gradient(180deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette.background.default, 0.5)} 100%)`
          }}
        >
          {dialogContent} {/* Render the determined content */}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseTechnicalAnalysis}>关闭</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // 渲染详细推荐对话框
  const renderDetailedRecommendationDialog = () => {
    if (!detailedRecommendation) return null;

    return (
      <Dialog
        open={!!detailedRecommendation} // Open if detailedRecommendation exists
        onClose={() => setDetailedRecommendation(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AutoAwesomeIcon color="primary" sx={{ mr: 1 }} />
            {detailedRecommendation?.symbol} 详细投资建议
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          {detailedRecommendation?.loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : detailedRecommendation?.data ? (
            <Box>
              <Typography variant="h6" gutterBottom>概述</Typography>
              <Typography variant="body1" paragraph>
                {detailedRecommendation.data.summary}
              </Typography>

              <Typography variant="h6" gutterBottom>建议理由</Typography>
              <List>
                {detailedRecommendation.data.reasons.map((reason, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      <Chip label={index + 1} size="small" />
                    </ListItemIcon>
                    <ListItemText primary={reason} />
                  </ListItem>
                ))}
              </List>

              <Typography variant="h6" gutterBottom>行动计划</Typography>
              <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                <List dense>
                  {detailedRecommendation.data.action_plan.map((step, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Chip
                          label={index + 1}
                          size="small"
                          color="primary"
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={step.title}
                        secondary={step.description}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>

              <Typography variant="h6" gutterBottom>详细分析</Typography>
              <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                {detailedRecommendation.data.analysis}
              </Typography>
            </Box>
          ) : (
            <Alert severity="error">无法加载详细建议。</Alert> // Handle error case
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailedRecommendation(null)}>关闭</Button>
        </DialogActions>
      </Dialog>
    );
  };

  // Fetch detailed AI analysis for a specific stock
  const handleFetchDetailedAiAnalysis = async (symbol) => {
    const position = strategyData.positions.find(p => p.symbol === symbol);
    if (!position) {
      toast.error(`无法找到 ${symbol} 的当前分析数据`);
      return;
    }

    setDetailedAiAnalysis({ symbol, loading: true, data: null, error: null });
    setIsDetailedAiDialogOpen(true);

    try {
      const baseUrl = import.meta.env.VITE_API_URL || API_BASE_URL;
      const apiUrl = `${baseUrl}/api/strategies/detailed-recommendation`;
      console.info(`Fetching detailed AI analysis from: ${apiUrl} for symbol: ${symbol}`);

      const response = await axios.post(apiUrl, {
        symbol,
        // Send relevant position data the backend endpoint expects
        position_data: {
          current_price: position.current_price, // Make sure these keys match backend API
          cost_price: position.avg_price,
          quantity: position.quantity,
          // Include any other fields the backend expects for its prompt
          rsi: position.rsi,
          recommendation: position.recommendation
          // Add more fields if the backend uses them (e.g., volatility, trend_strength)
        },
        account_id: selectedAccount
      });

      console.info(`Received detailed AI analysis response status: ${response.status}`);
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('AI分析返回了无效的数据格式。');
      }
      setDetailedAiAnalysis({ symbol, loading: false, data: response.data, error: null });

    } catch (error) {
      console.error('Error fetching detailed AI analysis:', error);
      let errorMsg = `无法获取 ${symbol} 的详细建议。`;
      if (axios.isAxiosError(error)) {
        if (error.response) {
          errorMsg = `AI分析错误 (${error.response.status}): ${error.response.data?.error || error.response.data?.message || error.response.statusText}`;
        } else if (error.request) {
          errorMsg = 'AI分析请求失败，请检查网络连接。'
        } else {
          errorMsg = error.message;
        }
      } else if (error instanceof Error) {
        errorMsg = error.message;
      }
      setDetailedAiAnalysis({
        symbol,
        loading: false,
        data: null,
        error: errorMsg
      });
      toast.error(errorMsg);
    }
  };

  const formatTimestamp = (ts) => ts ? String(ts) : 'N/A'; // Add this line

  // --- NEW: Handle Model Future Predictions ---
  const handleOpenFuturePredictions = async (symbol) => {
    setSelectedSymbolForPredictions(symbol);
    setOpenFuturePredictionsDialog(true);
    setIsFetchingPredictions(true);
    setPredictionsError(null);
    setFuturePredictionsData(null); // Clear previous data

    // Cancel previous request if any
    if (futurePredictionsAbortControllerRef.current) {
      futurePredictionsAbortControllerRef.current.abort();
    }
    // Create a new AbortController for the current request
    futurePredictionsAbortControllerRef.current = new AbortController();

    try {
      const response = await axios.get(`${API_BASE_URL}/api/strategies/get_model_future_predictions`, {
        params: { symbol },
        signal: futurePredictionsAbortControllerRef.current.signal // Pass the signal
      });

      const responseData = response.data;

      if (
        responseData &&
        typeof responseData.plot_image_base64 !== 'undefined' &&
        responseData.future_predictions_data !== undefined &&
        Array.isArray(responseData.future_predictions_data)
      ) {
        setFuturePredictionsData(responseData);
        // The FuturePredictionsDialog component is responsible for displaying messages
        // like "No data available" if plot_image_base64 is null and future_predictions_data is empty.
      } else {
        const errorMsg = '从API收到的未来预测数据格式不正确。';
        console.error(errorMsg, "Raw response data:", responseData);
        setPredictionsError(errorMsg); // This error will be displayed in the dialog
      }
    } catch (err) {
      if (axios.isCancel(err)) {
        console.log('Future predictions fetch aborted:', symbol);
        // Optionally set a specific state or do nothing if aborted
        // If a message is desired for cancellation, setPredictionsError here.
      } else {
        console.error(`Error fetching future predictions for ${symbol}:`, err);
        const errorMsg = err.response?.data?.error || err.message || `获取 ${symbol} 未来预测失败。`;
        setPredictionsError(errorMsg); // This state is used by the FuturePredictionsDialog
      }
    } finally {
      setIsFetchingPredictions(false);
      // Clear the ref if the request completed or failed (not aborted)
      // Check if err is defined and then if it's a cancel error
      // Simplified: clear ref if the signal wasn't aborted (or if no signal reason)
      if (!(futurePredictionsAbortControllerRef.current?.signal?.aborted)) {
        futurePredictionsAbortControllerRef.current = null;
      }
    }
  };

  const handleCloseFuturePredictions = () => {
    setOpenFuturePredictionsDialog(false);
    setSelectedSymbolForPredictions('');
    // Optionally clear data and error
    // setFuturePredictionsData(null);
    // setPredictionsError(null);
  };
  // --- END: Handle Future Predictions ---

  if (loading && !accounts.length) {
    return (
      <>
        <NavBar />
        <Box sx={{ py: 4 }}>
          <Container maxWidth="lg">
            <LinearProgress />
          </Container>
        </Box>
      </>
    );
  }

  return (
    <>
      <NavBar />
      <Box sx={{ py: 3, bgcolor: theme.palette.background.default, minHeight: 'calc(100vh - 64px)' }}>
        <Container maxWidth="xl">
          {/* Display global loading only during initial account fetch */}
          {(loading && !accounts.length) && (
            <LinearProgress sx={{ mb: 2 }} />
          )}

          <GradientTypography
            variant="h4"
            gutterBottom
            sx={{
              mb: 4,
              fontSize: '2.2rem',
              textAlign: 'center',
              animation: 'fadeInDown 0.6s ease-out',
              '@keyframes fadeInDown': {
                '0%': {
                  opacity: 0,
                  transform: 'translateY(-20px)',
                },
                '100%': {
                  opacity: 1,
                  transform: 'translateY(0)',
                },
              },
            }}
          >
            策略分析
          </GradientTypography>

          {/* Display global error here, but not when analysis dialog is open */}
          {error && !showPositionAnalysis && (
            <Alert severity="error" sx={{ mb: 3 }}> {/* Adjusted margin */}
              {error}
            </Alert>
          )}

          {/* 主体区域 - 选择和指标 */}
          <Grid container spacing={2.5}>
            {/* Row 1: 账户选择 (Full Width) */}
            <Grid item xs={12}>
              <StyledCard>
                <CardContent sx={{ p: 2.5 }}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 2,
                    pb: 1,
                    borderBottom: theme => `1px solid ${theme.palette.divider}`
                  }}>
                    <AttachMoneyIcon sx={{ color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ ml: 1.5, fontWeight: 600 }}>选择账户</Typography>
                  </Box>
                  <FormControl fullWidth size="small" sx={{ mt: 1 }}>
                    <InputLabel>选择交易账户</InputLabel>
                    <Select
                      value={selectedAccount}
                      onChange={handleAccountChange}
                      label="选择交易账户"
                    >
                      {accounts.map((account) => (
                        <MenuItem key={account.id} value={account.id}>
                          {account.name}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </CardContent>
              </StyledCard>
            </Grid>

            {/* Row 2: Analysis Mode Tabs */}
            <Grid item xs={12}>
              <StyledCard>
                <CardContent sx={{ p: 0 }}>
                  <Tabs
                    value={analysisMode}
                    onChange={(e, newValue) => setAnalysisMode(newValue)}
                    sx={{ borderBottom: 1, borderColor: 'divider' }}
                  >
                    <Tab
                      label="传统策略分析"
                      value="traditional"
                      icon={<TimelineIcon />}
                      iconPosition="start"
                    />
                    <Tab
                      label="期权策略分析"
                      value="options"
                      icon={<SecurityIcon />}
                      iconPosition="start"
                    />
                  </Tabs>
                </CardContent>
              </StyledCard>
            </Grid>

            {/* Row 3: Content based on selected mode */}
            {analysisMode === 'traditional' ? (
              <>
                {/* Traditional Analysis - Stock Selection */}
                <Grid item xs={12}>
                  <StyledCard sx={{ minHeight: { md: 250 } }}> {/* Adjust height as needed */}
                    <CardContent sx={{ p: 2.5, display: 'flex', flexDirection: 'column', height: '100%' }}>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 2,
                        pb: 1,
                        borderBottom: theme => `1px solid ${theme.palette.divider}`
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <PieChartIcon sx={{ color: 'primary.main' }} />
                          <Typography variant="h6" sx={{ ml: 1.5, fontWeight: 600 }}>选择股票</Typography>
                        </Box>
                      </Box>

                      {/* Manual Stock Symbol Input */}
                      <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <Autocomplete
                          freeSolo
                          options={commonSymbols}
                          value={symbolInput}
                          onInputChange={(_, newValue) => setSymbolInput(newValue)}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              label="股票代码"
                              placeholder="输入股票代码，如 AAPL"
                              disabled={!selectedAccount}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault();
                                  handleAddSymbol();
                                }
                              }}
                            />
                          )}
                          sx={{ flexGrow: 1 }}
                        />
                        <Button
                          onClick={handleAddSymbol}
                          variant="outlined"
                          disabled={!symbolInput.trim() || !selectedAccount}
                        >
                          添加
                        </Button>
                      </Box>

                      <Box sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        gap: 1,
                        p: 1.5,
                        minHeight: 120,
                        maxHeight: 180,
                        overflowY: 'auto',
                        bgcolor: theme => alpha(theme.palette.background.default, 0.6),
                        borderRadius: 1,
                        border: theme => `1px dashed ${theme.palette.divider}`,
                        flexGrow: 1
                      }}>
                        {selectedStocks.length > 0 ? selectedStocks.map((symbol) => (
                          <Chip
                            key={symbol}
                            label={symbol}
                            onDelete={() => handleRemoveSymbol(symbol)}
                            color={availableStocks.position_symbols.includes(symbol) ? "primary" : "default"}
                            size="small"
                            sx={{ fontWeight: 500 }}
                          />
                        )) : (
                          <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '100%',
                            height: '100%'
                          }}>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 2, textAlign: 'center' }}>
                              请在上方输入股票代码进行分析
                            </Typography>
                            <SearchIcon color="disabled" fontSize="large" sx={{ mb: 1, opacity: 0.5 }} />
                          </Box>
                        )}
                      </Box>

                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          已选择: <Typography component="span" fontWeight="bold" color="primary">{selectedStocks.length}</Typography> 支股票
                          {selectedStocks.length > 0 && availableStocks.position_symbols.length > 0 && (
                            <Typography component="span" color="text.secondary">
                              (其中 <Typography component="span" fontWeight="bold" color="primary">
                                {selectedStocks.filter(s => availableStocks.position_symbols.includes(s)).length}
                              </Typography> 支为持仓股票)
                            </Typography>
                          )}
                        </Typography>
                      </Box>

                      {/* --- NEW: Analyze Selected Stocks Button --- */}
                      <Box sx={{ mt: 'auto', pt: 2 }}> {/* Place button at the bottom */}
                        <Button
                          fullWidth
                          variant="contained"
                          color="primary"
                          onClick={startAnalysis} // Call the function to analyze selected stocks
                          sx={{ py: 1.2 }} // Add padding
                        >
                          {isPositionAnalysisLoading ? '分析中...' : '分析已选股票'}
                        </Button>
                      </Box>
                    </CardContent>
                  </StyledCard>
                </Grid>

                {/* Row 3: 指标展示区域 (Risk & Performance Side-by-Side) */}
                <Grid item xs={12}>
                  <Grid container spacing={2.5} direction="column">
                    <Grid item>
                      <Grid container spacing={2.5}> {/* Ensure row direction for side-by-side */}
                        {/* 风险指标 */}
                        <Grid item xs={12} md={6}>
                          {renderMetricsCard(
                            '风险指标',
                            <GppGoodIcon color="error" />,
                            strategyData.risk_metrics,
                            RISK_METRIC_NAMES,
                            RISK_METRIC_DESCRIPTIONS
                          )}
                        </Grid>

                        {/* 绩效指标 */}
                        <Grid item xs={12} md={6}>
                          {renderMetricsCard(
                            '绩效指标',
                            <ShowChartIcon color="success" />,
                            strategyData.performance_metrics,
                            PERFORMANCE_METRIC_NAMES,
                            PERFORMANCE_METRIC_DESCRIPTIONS
                          )}
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                {/* 底部分析按钮区域 - 突出显示 */}
                <Grid item xs={12}>
                  <Grid container sx={{ mt: 2.5 }}>
                    {/* 查看详细分析按钮 - 条件渲染 */}
                    {showPositionAnalysis && (
                      <Grid container sx={{ mt: 2.5, display: 'flex', justifyContent: 'center' }}>
                        <Grid item>
                          <Button
                            variant="outlined"
                            onClick={() => setShowPositionAnalysis(true)}
                            startIcon={<TimelineIcon />}
                            sx={{ px: 3, py: 1 }} // Add some padding
                          >
                            查看详细持仓分析
                          </Button>
                        </Grid>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
              </>
            ) : (
              /* Options Analysis Mode */
              <Grid item xs={12}>
                <OptionsAnalysisPanel selectedAccount={selectedAccount} />
              </Grid>
            )}
          </Grid>

          {/* Render Dialogs */}
          {renderDetailedRecommendationDialog()}
          {renderPositionAnalysisDialog()}
          {renderDetailedAiDialog()}
          <FuturePredictionsDialog
            open={openFuturePredictionsDialog}
            onClose={handleCloseFuturePredictions}
            symbol={selectedSymbolForPredictions}
            isLoading={isFetchingPredictions}
            error={predictionsError}
            data={futurePredictionsData}
          />



          {/* Technical Analysis Dialog */}
          {renderTechnicalAnalysisDialog()}

          {/* Options Analysis Watchlist Dialog */}
          <WatchlistDialog
            open={watchlistDialogOpen}
            onClose={() => setWatchlistDialogOpen(false)}
            selectedAccount={selectedAccount}
            onWatchlistsUpdated={() => {
              // Refresh watchlists in OptionsAnalysisPanel if needed
              // This could be handled via a callback or state management
            }}
          />
        </Container>
      </Box>
    </>
  );
};

export default StrategiesPage; 